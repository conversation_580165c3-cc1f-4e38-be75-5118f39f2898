"settings" = "设置";
"game_duration" = "游戏时长";
"seconds" = "秒";
"language" = "语言";
"motion_sensitivity" = "运动灵敏度";
"low_sensitivity" = "低灵敏度";
"medium_sensitivity" = "中等灵敏度";
"high_sensitivity" = "高灵敏度";
"select_category" = "选择类别";
"start_game" = "开始游戏";
"hold_phone" = "把手机放在头顶";
"ready" = "准备好了";
"correct" = "正确";
"incorrect" = "错误";
"exit_game" = "退出游戏";
"exit_game_message" = "确定要退出游戏吗？";
"yes" = "是";
"no" = "否";
"words" = "单词";
"round" = "回合";
"start_team_game" = "队伍 %d 排名";
"new_game" = "新游戏";
"team" = "队伍";
"language_change_title" = "语言切换";
"language_change_message" = "为了使语言更改生效，需要重新启动应用程序。 您现在想重新启动吗？";
"restart_now" = "立即重启";
"cancel" = "取消";
"world_cuisine" = "世界美食";
"turkish_cuisine" = "土耳其美食";
"turkish_celebrities" = "土耳其名人";
"world_cities" = "世界城市";
"superheroes" = "超级英雄";
"footballers" = "足球运动员";
"historical_figures" = "历史人物";
"world_musicians" = "世界著名音乐家";
"world_actors" = "世界著名演员";
"bollywood_celebrities" = "宝莱坞名人";
"bollywood_celebrities.category" = "宝莱坞名人";
"locked_category" = "锁定类别";
"unlock_with_ad" = "观看广告解锁";
"watch_ad_to_unlock" = "观看广告以解锁此类别";
"unlock_success" = "类别已解锁！";
"unlock_failed" = "类别无法解锁";
"try_again_later" = "请稍后再试";
"ad_loading" = "广告加载中...";
"ad_not_available" = "目前没有广告";
"game_over" = "游戏结束";
"final_score" = "最终得分";
"play_again" = "再玩一次";
"back_to_menu" = "返回菜单";
"invalid_category" = "无效类别";
"invalid_category_message" = "未找到所选类别。";
"error" = "错误";
"ok" = "确定";
"animals" = "动物";
"animals.dog" = "狗";
"animals.cat" = "猫";
"animals.hamster" = "仓鼠";
"animals.rabbit" = "兔子";
"animals.parrot" = "鹦鹉";
"animals.goldfish" = "金鱼";
"animals.guinea_pig" = "豚鼠";
"animals.lion" = "狮子";
"animals.tiger" = "老虎";
"animals.elephant" = "长颈鹿";
"animals.giraffe" = "斑马";
"animals.zebra" = "猴子";
"animals.monkey" = "大猩猩";
"animals.gorilla" = "猎豹";
"animals.cheetah" = "犀牛";
"animals.rhinoceros" = "河马";
"animals.hippopotamus" = "海豚";
"animals.dolphin" = "鲸鱼";
"animals.shark" = "鲨鱼";
"animals.octopus" = "章鱼";
"animals.penguin" = "企鹅";
"animals.seahorse" = "海马";
"animals.jellyfish" = "水母";
"animals.eagle" = "鹰";
"animals.owl" = "猫头鹰";
"animals.sparrow" = "麻雀";
"animals.crow" = "乌鸦";
"animals.peacock" = "孔雀";
"animals.flamingo" = "火烈鸟";
"animals.toucan" = "巨嘴鸟";
"animals.snake" = "蛇";
"animals.crocodile" = "鳄鱼";
"animals.turtle" = "乌龟";
"animals.lizard" = "蜥蜴";
"animals.chameleon" = "变色龙";
"animals.iguana" = "鬣蜥";
"animals.butterfly" = "蝴蝶";
"animals.bee" = "蜜蜂";
"bollywood_celebrities.shah_rukh_khan" = "沙鲁克·罕";
"bollywood_celebrities.amitabh_bachchan" = "阿米塔·巴强";
"bollywood_celebrities.aamir_khan" = "阿米尔·汗";
"bollywood_celebrities.salman_khan" = "萨尔曼·汗";
"bollywood_celebrities.akshay_kumar" = "阿克沙耶·库马尔";
"bollywood_celebrities.hrithik_roshan" = "赫里尼克·罗斯汉";
"bollywood_celebrities.ranbir_kapoor" = "兰比尔·卡普尔";
"bollywood_celebrities.ranveer_singh" = "兰维尔·辛格";
"bollywood_celebrities.varun_dhawan" = "瓦伦·达万";
"bollywood_celebrities.arjun_kapoor" = "阿尔琼·卡普尔";
"bollywood_celebrities.rajkummar_rao" = "拉吉库马尔·拉奥";
"bollywood_celebrities.ayushmann_khurrana" = "阿尤斯曼·库拉纳";
"bollywood_celebrities.kartik_aaryan" = "卡尔蒂克·阿亚恩";
"bollywood_celebrities.vickey_kaushal" = "维基·考沙尔";
"bollywood_celebrities.shahid_kapoor" = "沙希德·卡普尔";
"bollywood_celebrities.deepika_padukone" = "迪皮卡·帕度柯妮";
"bollywood_celebrities.priyanka_chopra" = "朴雅卡·乔普拉";
"bollywood_celebrities.katrina_kaif" = "卡特莉娜·卡芙";
"bollywood_celebrities.alia_bhatt" = "阿莉雅·巴特";
"bollywood_celebrities.kareena_kapoor" = "卡琳娜·卡普尔";
"bollywood_celebrities.anushka_sharma" = "安努舒卡·莎玛";
"bollywood_celebrities.sonam_kapoor" = "索南·卡普尔";
"bollywood_celebrities.jacqueline_fernandez" = "杰奎琳·费尔南德斯";
"bollywood_celebrities.shraddha_kapoor" = "施拉达·卡普尔";
"bollywood_celebrities.parineeti_chopra" = "朴雅卡·乔普拉";
"bollywood_celebrities.kriti_sanon" = "克里蒂·萨农";
"bollywood_celebrities.taapsee_pannu" = "塔普西·潘努";
"bollywood_celebrities.bhumi_pednekar" = "布米·佩德卡尔";
"bollywood_celebrities.kangana_ranaut" = "康格娜·拉瑙特";
"bollywood_celebrities.vidya_balan" = "维迪亚·巴兰";
"bollywood_celebrities.madhuri_dixit" = "玛杜里·迪克西特";
"bollywood_celebrities.sridevi" = "希里黛玉";
"bollywood_celebrities.rekha" = "瑞卡";
"bollywood_celebrities.aishwarya_rai" = "艾西瓦娅·雷";
"bollywood_celebrities.sushmita_sen" = "苏斯米塔·森";
"bollywood_celebrities.malaika_arora" = "玛莱卡·阿罗拉";
"bollywood_celebrities.bipasha_basu" = "比帕莎·芭素";
"bollywood_celebrities.chitrangada_singh" = "奇特朗加达·辛格";
"bollywood_celebrities.nimrat_kaur" = "妮姆拉特·考尔";
"bollywood_celebrities.richa_chadha" = "里查·查达";
"bollywood_celebrities.konkona_sen" = "孔珂娜·森";
"bollywood_celebrities.kalki_koechlin" = "卡尔基·克什林";
"bollywood_celebrities.radhika_apte" = "拉迪卡·艾普特";
"bollywood_celebrities.tillotama_shome" = "蒂洛塔玛·尚姆";
"bollywood_celebrities.nawazuddin_siddiqui" = "纳瓦祖丁·西迪基";
"bollywood_celebrities.irrfan_khan" = "伊尔凡·可汗";
"bollywood_celebrities.manoj_bajpayee" = "曼诺·巴杰帕伊";
"bollywood_celebrities.pankaj_tripathi" = "潘卡·特里帕蒂";
"bollywood_celebrities.kay_kay_menon" = "凯·凯·梅农";
"animals.ant" = "蚂蚁";
"animals.spider" = "蜘蛛";
"animals.grasshopper" = "蚱蜢";
"animals.ladybug" = "瓢虫";
"animals.cow" = "奶牛";
"animals.sheep" = "绵羊";
"animals.goat" = "山羊";
"animals.pig" = "猪";
"animals.horse" = "马";
"animals.chicken" = "鸡";
"animals.duck" = "鸭子";
"footballers.lionel_messi" = "莱昂内尔·梅西";
"footballers.cristiano_ronaldo" = "克里斯蒂亚诺·罗纳尔多";
"footballers.pele" = "贝利";
"footballers.diego_maradona" = "迭戈·马拉多纳";
"footballers.johan_cruyff" = "约翰·克鲁伊夫";
"footballers.zinedine_zidane" = "齐内丁·齐达内";
"footballers.ronaldo_nazario" = "罗纳尔多·纳扎里奥";
"footballers.ronaldinho_gaucho" = "罗纳尔迪尼奥";
"footballers.michel_platini" = "米歇尔·普拉蒂尼";
"footballers.marco_van_basten" = "马尔科·范·巴斯滕";
"footballers.george_best" = "乔治·贝斯特";
"footballers.garrincha" = "加林查";
"footballers.franz_beckenbauer" = "弗朗茨·贝肯鲍尔";
"footballers.alfredo_di_stefano" = "阿尔弗雷多·迪·斯蒂法诺";
"footballers.ferenc_puskas" = "费伦茨·普斯卡什";
"footballers.paolo_maldini" = "保罗·马尔蒂尼";
"footballers.andres_iniesta" = "安德烈斯·伊涅斯塔";
"footballers.xavi_hernandez" = "哈维·埃尔南德斯";
"footballers.roberto_baggio" = "罗伯托·巴乔";
"footballers.luis_suarez" = "路易斯·苏亚雷斯";
"footballers.thierry_henry" = "蒂埃里·亨利";
"footballers.eusebio" = "尤西比奥";
"footballers.gerd_muller" = "盖德·穆勒";
"footballers.kaka" = "卡卡";
"footballers.lothar_matthaus" = "洛塔尔·马特乌斯";
"footballers.romario" = "罗马里奥";
"footballers.david_beckham" = "大卫·贝克汉姆";
"footballers.raul_gonzalez" = "劳尔·冈萨雷斯";
"footballers.bobby_charlton" = "博比·查尔顿";
"footballers.dennis_bergkamp" = "丹尼斯·博格坎普";
"footballers.zlatan_ibrahimovic" = "兹拉坦·伊布拉希莫维奇";
"footballers.andrea_pirlo" = "安德烈·皮尔洛";
"footballers.frank_lampard" = "弗兰克·兰帕德";
"footballers.steven_gerrard" = "史蒂文·杰拉德";
"footballers.wayne_rooney" = "韦恩·鲁尼";
"footballers.didier_drogba" = "迪迪埃·德罗巴";
"footballers.samuel_etoo" = "塞缪尔·埃托奥";
"footballers.ryan_giggs" = "瑞恩·吉格斯";
"footballers.george_weah" = "乔治·维阿";
"footballers.lev_yashin" = "列夫·雅辛";
"footballers.javier_zanetti" = "哈维尔·萨内蒂";
"footballers.dino_zoff" = "迪诺·佐夫";
"footballers.ruud_gullit" = "鲁德·古利特";
"footballers.roberto_carlos" = "罗伯托·卡洛斯";
"footballers.cafu" = "卡福";
"footballers.francesco_totti" = "弗朗切斯科·托蒂";
"footballers.claudio_caniggia" = "克劳迪奥·卡尼吉亚";
"footballers.michael_laudrup" = "米歇尔·劳德鲁普";
"footballers.hristo_stoichkov" = "赫里斯托·斯托伊奇科夫";
"footballers.paul_scholes" = "保罗·斯科尔斯";
"footballers.gianluigi_buffon" = "吉安路易吉·布冯";
"footballers.manuel_neuer" = "曼努埃尔·诺伊尔";
"footballers.philipp_lahm" = "菲利普·拉姆";
"footballers.sergio_ramos" = "塞尔吉奥·拉莫斯";
"footballers.carles_puyol" = "卡尔斯·普约尔";
"footballers.alessandro_del_piero" = "亚历山德罗·德尔·皮耶罗";
"footballers.robert_lewandowski" = "罗伯特·莱万多夫斯基";
"footballers.luka_modric" = "卢卡·莫德里奇";
"footballers.sandro_mazzola" = "桑德罗·马佐拉";
"footballers.juan_roman_riquelme" = "胡安·罗马·里克尔梅";
"footballers.socrates" = "苏格拉底";
"footballers.kenny_dalglish" = "肯尼·达格利什";
"footballers.jairzinho" = "雅伊济尼奥";
"footballers.neymar_jr" = "内马尔";
"footballers.sadio_mane" = "萨迪奥·马内";
"footballers.mohamed_salah" = "穆罕默德·萨拉赫";
"footballers.kylian_mbappe" = "基利安·姆巴佩";
"footballers.karim_benzema" = "卡里姆·本泽马";
"footballers.paul_pogba" = "保罗·博格巴";
"footballers.rivaldo" = "里瓦尔多";
"footballers.david_villa" = "大卫·比利亚";
"footballers.cesc_fabregas" = "塞斯克·法布雷加斯";
"footballers.mesut_ozil" = "梅苏特·厄齐尔";
"footballers.pavel_nedved" = "帕维尔·内德维德";
"footballers.patrick_vieira" = "帕特里克·维埃拉";
"footballers.claude_makelele" = "克劳德·马克莱莱";
"footballers.fernando_torres" = "费尔南多·托雷斯";
"footballers.gheorghe_hagi" = "格奥尔基·哈吉";
"footballers.davor_suker" = "达沃·苏克";
"footballers.antonio_di_natale" = "安东尼奥·迪·纳塔莱";
"footballers.alessandro_nesta" = "亚历山德罗·内斯塔";
"footballers.edinson_cavani" = "埃丁森·卡瓦尼";
"footballers.jurgen_klinsmann" = "尤尔根·克林斯曼";
"footballers.arjen_robben" = "阿尔扬·罗本";
"footballers.bastian_schweinsteiger" = "巴斯蒂安·施魏因施泰格";
"footballers.hugo_sanchez" = "乌戈·桑切斯";
"footballers.gary_lineker" = "加里·莱因克尔";
"footballers.alan_shearer" = "阿兰·希勒";
"footballers.gonzalo_higuain" = "冈萨洛·伊瓜因";
"footballers.david_silva" = "大卫·席尔瓦";
"footballers.eric_cantona" = "埃里克·坎通纳";
"footballers.christian_vieri" = "克里斯蒂安·维埃里";
"footballers.miroslav_klose" = "米洛斯拉夫·克洛泽";
"footballers.ngolo_kante" = "恩戈洛·坎特";
"footballers.toni_kroos" = "托尼·克罗斯";
"footballers.gareth_bale" = "加雷斯·贝尔";
"footballers.diego_forlan" = "迭戈·弗兰";
"footballers.marcelo_vieira" = "马塞洛·维埃拉";
"footballers.xabi_alonso" = "哈维·阿隆索";
"footballers.ivan_rakitic" = "伊万·拉基蒂奇";
"footballers.angel_di_maria" = "安赫尔·迪马里亚";
"footballers.patrick_kluivert" = "帕特里克·克鲁伊维特";
"footballers.ederson" = "埃德森";
"footballers.marc_andre_ter_stegen" = "马克-安德烈·特尔施特根";
"footballers.keylor_navas" = "凯洛尔·纳瓦斯";
"footballers.hugo_lloris" = "雨果·洛里斯";
"footballers.david_de_gea" = "大卫·德赫亚";
"footballers.jan_oblak" = "扬·奥布拉克";
"footballers.jordan_pickford" = "乔丹·皮克福德";
"footballers.nick_pope" = "尼克·波普";
"footballers.dean_henderson" = "迪恩·亨德森";
"footballers.samir_handanovic" = "萨米尔·汉达诺维奇";
"footballers.wojciech_szczesny" = "沃伊切赫·什琴斯尼";
"footballers.rui_patricio" = "鲁伊·帕特里西奥";
"footballers.kasper_schmeichel" = "卡斯帕·舒梅切尔";
"footballers.romelu_lukaku" = "罗梅卢·卢卡库";
"footballers.harry_kane" = "哈里·凯恩";
"footballers.robert_lewandowski" = "罗伯特·莱万多夫斯基";
"footballers.karim_benzema" = "卡里姆·本泽马";
"footballers.luis_suarez" = "路易斯·苏亚雷斯";
"footballers.sergio_aguero" = "塞尔吉奥·阿奎罗";
"footballers.pierre_emerick_aubameyang" = "皮埃尔-埃默里克·奥巴梅扬";
"footballers.jamie_vardy" = "杰米·瓦尔迪";
"footballers.diego_costa" = "迭戈·科斯塔";
"footballers.alvaro_morata" = "阿尔瓦罗·莫拉塔";
"footballers.mario_mandzukic" = "马里奥·曼朱基奇";
"footballers.edinson_cavani" = "埃丁森·卡瓦尼";
"footballers.gonzalo_higuain" = "贡萨洛·伊瓜因";
"footballers.antoine_griezmann" = "安托万·格里兹曼";
"footballers.paul_pogba" = "保罗·博格巴";
"footballers.ngolo_kante" = "恩戈洛·坎特";
"footballers.toni_kroos" = "托尼·克罗斯";
"footballers.luka_modric" = "卢卡·莫德里奇";
"footballers.casemiro" = "卡塞米罗";
"footballers.sergio_busquets" = "塞尔吉奥·布斯克茨";
"footballers.jordan_henderson" = "乔丹·亨德森";
"footballers.dele_alli" = "德勒·阿里";
"footballers.christian_eriksen" = "克里斯蒂安·埃里克森";
"footballers.kevin_de_bruyne" = "凯文·德布劳内";
"footballers.david_silva" = "大卫·席尔瓦";
"footballers.bernardo_silva" = "贝尔纳多·席尔瓦";
"footballers.bruno_fernandes" = "布鲁诺·费尔南德斯";
"footballers.jack_grealish" = "杰克·格里利什";
"footballers.mason_mount" = "梅森·芒特";
"footballers.phil_foden" = "菲尔·福登";
"footballers.james_maddison" = "詹姆斯·麦迪逊";
"footballers.marcus_rashford" = "马库斯·拉什福德";
"footballers.raheem_sterling" = "拉希姆·斯特林";
"footballers.mohamed_salah" = "穆罕默德·萨拉赫";
"footballers.sadio_mane" = "萨迪奥·马内";
"footballers.riyad_mahrez" = "里亚德·马赫雷斯";
"footballers.sterling" = "拉希姆·斯特林";
"footballers.son_heung_min" = "孙兴慜";
"footballers.jadon_sancho" = "杰登·桑乔";
"footballers.vinicius_junior" = "维尼修斯·儒尼奥尔";
"footballers.rodrygo" = "罗德里戈";
"footballers.ansu_fati" = "安苏·法蒂";
"footballers.ferran_torres" = "费兰·托雷斯";
"footballers.pedri" = "佩德里";
"footballers.gavi" = "加维";
"footballers.nicolo_barella" = "尼科洛·巴雷拉";
"footballers.federico_chiesa" = "费德里科·基耶萨";
"footballers.lorenzo_insigne" = "洛伦佐·因西涅";
"footballers.ciro_immobile" = "西罗·因莫比莱";
"footballers.romelu_lukaku" = "罗梅卢·卢卡库";
"footballers.thorgan_hazard" = "托尔冈·阿扎尔";
"footballers.yannick_carrasco" = "扬尼克·卡拉斯科";
"footballers.dries_mertens" = "德里斯·梅滕斯";
"footballers.eden_hazard" = "埃登·阿扎尔";
"footballers.kevin_de_bruyne" = "凯文·德布劳内";
"footballers.erling_haaland" = "埃尔林·哈兰德";
"footballers.joshua_kimmich" = "约书亚·基米希";
"footballers.leon_goretzka" = "莱昂·戈雷茨卡";
"footballers.kai_havertz" = "凯·哈弗茨";
"footballers.timo_werner" = "蒂莫·维尔纳";
"footballers.serge_gnabry" = "塞尔日·格纳布里";
"footballers.leroy_sane" = "勒罗伊·萨内";
"footballers.thomas_muller" = "托马斯·穆勒";
"footballers.marco_reus" = "马尔科·罗伊斯";
"footballers.jadon_sancho" = "杰登·桑乔";
"footballers.erling_haaland" = "埃尔林·哈兰德";
"footballers.martin_odegaard" = "马丁·厄德高";
"footballers.haaland" = "埃尔林·哈兰德";
"footballers.sancho" = "杰登·桑乔";
"footballers.foden" = "菲尔·福登";
"footballers.mount" = "梅森·芒特";
"footballers.rice" = "德克兰·赖斯";
"footballers.sterling" = "拉希姆·斯特林";
"footballers.kane" = "哈里·凯恩";
"footballers.rashford" = "马库斯·拉什福德";
"footballers.grealish" = "杰克·格里利什";
"footballers.saka" = "布卡约·萨卡";
"footballers.maguire" = "哈里·马奎尔";
"footballers.shaw" = "卢克·肖";
"footballers.walker" = "凯尔·沃克";
"footballers.stones" = "约翰·斯通斯";
"footballers.pickford" = "乔丹·皮克福德";
"footballers.henderson" = "乔丹·亨德森";
"footballers.pickford" = "乔丹·皮克福德";
"footballers.pope" = "尼克·波普";
"footballers.henderson" = "迪恩·亨德森";
"footballers.maguire" = "哈里·马奎尔";
"footballers.stones" = "约翰·斯通斯";
"footballers.walker" = "凯尔·沃克";
"footballers.shaw" = "卢克·肖";
"footballers.trippier" = "基兰·特里皮尔";
"footballers.james" = "里斯·詹姆斯";
"footballers.chilwell" = "本·奇尔韦尔";
"footballers.mings" = "泰隆·明斯";
"footballers.coady" = "康纳·考迪";
"footballers.white" = "本·怀特";
"footballers.pickford" = "乔丹·皮克福德";
"footballers.pope" = "尼克·波普";
"footballers.henderson" = "迪恩·亨德森";
"footballers.maguire" = "哈里·马奎尔";
"footballers.stones" = "约翰·斯通斯";
"footballers.walker" = "凯尔·沃克";
"footballers.shaw" = "卢克·肖";
"footballers.trippier" = "基兰·特里皮尔";
"footballers.james" = "里斯·詹姆斯";
"footballers.chilwell" = "本·奇尔韦尔";
"footballers.mings" = "泰隆·明斯";
"footballers.coady" = "康纳·考迪";
"footballers.white" = "本·怀特";
"footballers.pickford" = "乔丹·皮克福德";
"footballers.pope" = "尼克·波普";
"footballers.henderson" = "迪恩·亨德森";
"footballers.maguire" = "哈里·马奎尔";
"footballers.stones" = "约翰·斯通斯";
"footballers.walker" = "凯尔·沃克";
"footballers.shaw" = "卢克·肖";
"footballers.trippier" = "基兰·特里皮尔";
"footballers.james" = "里士满·詹姆斯";
"footballers.chilwell" = "本·奇尔韦尔";
"footballers.mings" = "泰隆·明斯";
"footballers.coady" = "康纳·考迪";
"footballers.white" = "本·怀特";
"pizza" = "披萨";
"sushi" = "寿司";
"hamburger" = "汉堡";
"pasta" = "意大利面";
"tacos" = "章鱼小丸子";
"paella" = "西班牙海鲜饭";
"dimsum" = "点心";
"kebab" = "烤肉";
"curry" = "咖喱";
"ramen" = "拉面";
"pasta_carbonara" = "意式培根蛋面";
"lasagna" = "千层面";
"risotto" = "意大利调味饭";
"tiramisu" = "提拉米苏";
"bruschetta" = "意式烤面包";
"gelato" = "土耳其冰淇淋";
"croissant" = "可颂";
"coq_au_vin" = "红酒焖鸡";
"ratatouille" = "普罗旺斯炖菜";
"creme_brulee" = "焦糖布丁";
"bouillabaisse" = "马赛鱼汤";
"french_onion_soup" = "法式洋葱汤";
"macaron" = "马卡龙";
"burger" = "汉堡";
"hot_dog" = "热狗";
"bbq_ribs" = "烤肋排";
"buffalo_wings" = "水牛城鸡翅";
"apple_pie" = "苹果派";
"mac_and_cheese" = "通心粉和奶酪";
"cheesecake" = "芝士蛋糕";
"peking_duck" = "北京烤鸭";
"sweet_and_sour_pork" = "糖醋里脊";
"dumplings" = "饺子 (Manti)";
"spring_rolls" = "春卷 (Bahar Rollu)";
"kung_pao_chicken" = "宫保鸡丁 (Kung Pao Tavuk)";
"fried_rice" = "炒饭 (Kızarmış Pirinç)";
"hot_pot" = "火锅 (Hot Pot)";
"tempura" = "天妇罗 (Tempura)";
"yakitori" = "烤鸡肉串 (Yakitori)";
"miso_soup" = "味噌汤 (Miso Çorbası)";
"takoyaki" = "章鱼小丸子 (Takoyaki)";
"okonomiyaki" = "大阪烧 (Okonomiyaki)";
"chicken_tikka_masala" = "提卡玛萨拉";
"biryani" = "印度香饭";
"naan" = "馕";
"dal_tadka" = "黄油扁豆";
"butter_chicken" = "黄油鸡";
"samosa" = "萨摩萨三角饺";
"chana_masala" = "鹰嘴豆玛萨拉";
"tapas" = "西班牙小吃";
"gazpacho" = "西班牙凉菜汤";
"churros" = "吉事果";
"tortilla_espanola" = "西班牙土豆煎蛋饼";
"jamon_iberico" = "伊比利亚火腿";
"pisto" = "普isto蔬菜烩";
"burrito" = "墨西哥卷饼";
"quesadilla" = "墨西哥芝士饼";
"guacamole" = "鳄梨酱";
"chili_con_carne" = "辣肉酱";
"tamales" = "玉米粉蒸肉";
"moussaka" = "茄盒";
"souvlaki" = "希腊烤肉串";
"tzatziki" = "扎吉克";
"dolmades" = "酿葡萄叶";
"greek_salad" = "希腊沙拉";
"spanakopita" = "菠菜馅饼";
"pad_thai" = "泰式炒河粉";
"green_curry" = "绿咖喱";
"tom_yum_soup" = "冬阴功汤";
"massaman_curry" = "玛莎曼咖喱";
"mango_sticky_rice" = "芒果糯米饭";
"som_tam" = "青木瓜沙拉";
"satay" = "沙嗲";
"kimchi" = "泡菜";
"bibimbap" = "石锅拌饭";
"bulgogi" = "烤牛肉";
"tteokbokki" = "炒年糕";
"japchae" = " 잡채 (粉丝杂烩)";
"samgyeopsal" = "五花肉";
"kimchi_jjigae" = "泡菜汤";
"hummus" = "鹰嘴豆泥";
"falafel" = "炸豆丸子";
"shawarma" = "沙瓦玛";
"baba_ganoush" = "巴巴 · 盖努什";
"tabbouleh" = "塔布勒沙拉";
"knafeh" = "库纳法";
"mansaf" = "曼萨夫";
"bratwurst" = "香肠";
"sauerbraten" = "德国酸牛肉";
"pretzel" = "椒盐卷饼";
"schnitzel" = "炸肉排";
"kartoffelsalat" = "土豆沙拉";
"currywurst" = "咖喱香肠";
"apfelstrudel" = "苹果卷";
"feijoada" = "巴西黑豆炖肉";
"pao_de_queijo" = "奶酪面包";
"brigadeiro" = "巧克力球";
"acai_bowl" = "巴西莓碗";
"moqueca" = "巴西海鲜炖菜";
"churrasco" = "烤肉";
"coxinha" = "炸鸡肉丸";
"kebap" = "烤肉串";
"lahmacun" = "土耳其披萨";
"pide" = "土耳其馅饼";
"döner" = "烤肉";
"mantı" = "土耳其饺子";
"karnıyarık" = "酿茄子";
"imam_bayıldı" = "牧师晕倒";
"menemen" = "土耳其炒蛋";
"baklava" = "果仁蜜饼";
"künefe" = "卡达伊夫";
"iskender" = "伊斯坎德烤肉";
"adana_kebabı" = "阿达纳烤肉";
"urfa_kebabı" = "乌尔法烤肉";
"börek" = "博雷克";
"çiğ_köfte" = "生肉丸";
"hünkâr_beğendi" = "苏丹喜欢的茄子泥";
"zeytinyağlı_yaprak_sarma" = "橄榄油拌卷叶包";
"dolma" = "多尔玛（酿菜）";
"kuru_fasulye" = "干豆";
"pilav" = "米饭";
"mercimek_çorbası" = "扁豆汤";
"ezogelin_çorbası" = "埃佐 gelin 汤";
"tarhana_çorbası" = "塔哈纳汤";
"tavuklu_pilav" = "鸡肉饭";
"balık_ekmek" = "烤鱼三明治";
"sucuklu_yumurta" = "香肠鸡蛋";
"gözleme" = "土耳其馅饼";
"kaşarlı_tost" = "芝士吐司";
"midye_dolma" = "贻贝酿饭";
"kokoreç" = "烤羊肠";
"paça_çorbası" = "猪蹄汤";
"işkembe_çorbası" = "羊肚汤";
"tandır_kebabı" = "烤肉";
"cağ_kebabı" = "土耳其旋转烤肉";
"testi_kebabı" = "罐焖烤肉";
"ali_nazik" = "阿里纳泽克";
"beyti_kebabı" = "贝蒂烤肉";
"patlıcan_kebabı" = "茄子烤肉";
"cızlak_kebabı" = "兹拉克烤肉";
"kağıt_kebabı" = "纸烤肉";
"ciğer_şiş" = "烤羊肉串";
"perde_pilavı" = "拌米饭";
"hamsili_pilav" = "凤尾鱼饭";
"tepsi_kebabı" = "烤盘烤肉";
"kapuska" = "卷心菜炖肉";
"içli_köfte" = "肉馅炸丸子";
"arnavut_ciğeri" = "阿尔巴尼亚炸肝";
"tirit" = "汤泡肉";
"keşkek" = "土耳其肉麦粥";
"mıhlama" = "玉米面糊";
"kuzu_güveç" = "炖羊肉";
"şakşuka" = "沙克舒卡";
"zeytinyağlı_enginar" = "橄榄油朝鲜蓟";
"taze_fasulye" = "鲜嫩四季豆";
"kabak_çiçeği_dolması" = "西葫芦花酿";
"pazı_sarması" = "瑞士甜菜卷";
"kadayıf" = "卡达伊夫";
"revani" = "雷瓦尼";
"şekerpare" = "谢凯帕莱";
"kazandibi" = "烤鸡丝";
"sütlaç" = "米布丁";
"aşure" = "阿舒雷";
"lokum" = "软糖";
"helva" = " халва （哈瓦，一种甜点）";
"tahin_pekmez" = "芝麻酱蜜饯";
"cevizli_sucuk" = "核桃肠";
"zerde" = "泽代 (一种甜品)";
"irmik_helvası" = "粗面粉哈瓦";
"trileçe" = "特里列切";
"fırın_sütlaç" = "烤米布丁";
"tavukgöğsü" = "鸡丝布丁";
"kaymak" = "奶油凝乳";
"ayva_tatlısı" = "榅桲蜜饯";
"güllaç" = "古拉奇";
"ekmek_kadayıfı" = "面包卡达伊夫";
"yoğurt" = "酸奶";
"ayran" = "艾伦";
"şalgam" = "芜菁汁";
"boza" = "博扎";
"türk_kahvesi" = "土耳其咖啡";
"çay" = "茶";
"salep" = "兰花茶";
"kuşburnu_çayı" = "玫瑰果茶";
"osmanlı_şerbetleri" = "奥斯曼果子露";
"world_cities.new_york" = "纽约";
"world_cities.los_angeles" = "洛杉矶";
"world_cities.london" = "伦敦";
"world_cities.paris" = "巴黎";
"world_cities.rome" = "罗马";
"world_cities.berlin" = "柏林";
"world_cities.madrid" = "马德里";
"world_cities.barcelona" = "巴塞罗那";
"world_cities.istanbul" = "伊斯坦布尔";
"world_cities.dubai" = "迪拜";
"world_cities.tokyo" = "东京";
"world_cities.kyoto" = "京都";
"world_cities.beijing" = "北京";
"world_cities.shanghai" = "上海";
"world_cities.hong_kong" = "香港";
"world_cities.bangkok" = "曼谷";
"world_cities.singapore" = "新加坡";
"world_cities.seoul" = "独自";
"world_cities.moscow" = "莫斯科";
"world_cities.st_petersburg" = "圣彼得堡";
"world_cities.sydney" = "悉尼";
"world_cities.melbourne" = "墨尔本";
"world_cities.rio_de_janeiro" = "里约热内卢";
"world_cities.sao_paulo" = "圣保罗";
"world_cities.buenos_aires" = "布宜诺斯艾利斯";
"world_cities.mexico_city" = "墨西哥城";
"world_cities.toronto" = "多伦多";
"world_cities.vancouver" = "温哥华";
"world_cities.chicago" = "芝加哥";
"world_cities.san_francisco" = "旧金山";
"world_cities.washington_dc" = "华盛顿特区";
"world_cities.las_vegas" = "拉斯维加斯";
"world_cities.miami" = "迈阿密";
"world_cities.boston" = "波士顿";
"world_cities.amsterdam" = "阿姆斯特丹";
"world_cities.brussels" = "布鲁塞尔";
"world_cities.vienna" = "维也纳";
"world_cities.prague" = "布拉格";
"world_cities.budapest" = "布达佩斯";
"world_cities.copenhagen" = "哥本哈根";
"world_cities.stockholm" = "斯德哥尔摩";
"world_cities.oslo" = "奥斯陆";
"world_cities.helsinki" = "赫尔辛基";
"world_cities.dublin" = "都柏林";
"world_cities.athens" = "雅典";
"world_cities.warsaw" = "华沙";
"world_cities.lisbon" = "里斯本";
"world_cities.venice" = "威尼斯";
"world_cities.milan" = "米兰";
"world_cities.florence" = "佛罗伦萨";
"world_cities.naples" = "那不勒斯";
"world_cities.zurich" = "苏黎世";
"world_cities.geneva" = "日内瓦";
"world_cities.munich" = "慕尼黑";
"world_cities.frankfurt" = "法兰克福";
"world_cities.hamburg" = "汉堡";
"world_cities.johannesburg" = "约翰内斯堡";
"world_cities.cape_town" = "开普敦";
"world_cities.casablanca" = "卡萨布兰卡";
"world_cities.marrakech" = "马拉喀什";
"world_cities.ankara" = "安卡拉";
"world_cities.jerusalem" = "耶路撒冷";
"world_cities.cairo" = "开罗";
"world_cities.alexandria" = "亚历山大港";
"world_cities.new_delhi" = "新德里";
"world_cities.mumbai" = "孟买";
"world_cities.bangalore" = "班加罗尔";
"world_cities.jakarta" = "雅加达";
"world_cities.kuala_lumpur" = "吉隆坡";
"world_cities.ho_chi_minh_city" = "胡志明市";
"world_cities.hanoi" = "河内";
"world_cities.manila" = "马尼拉";
"world_cities.taipei" = "台北";
"world_cities.doha" = "多哈";
"world_cities.abu_dhabi" = "阿布扎比";
"world_cities.riyadh" = "利雅得";
"world_cities.mecca" = "麦加";
"world_cities.medina" = "麦地那";
"world_cities.tehran" = "德黑兰";
"world_cities.baghdad" = "巴格达";
"world_cities.karachi" = "卡拉奇";
"world_cities.lagos" = "拉各斯";
"world_cities.nairobi" = "内罗毕";
"world_cities.accra" = "阿克拉";
"world_cities.perth" = "珀斯";
"world_cities.brisbane" = "布里斯班";
"world_cities.auckland" = "奥克兰";
"world_cities.wellington" = "惠灵顿";
"world_cities.kathmandu" = "加德满都";
"world_cities.havana" = "哈瓦那";
"world_cities.santiago" = "圣地亚哥";
"world_cities.bogota" = "波哥大";
"world_cities.lima" = "利马";
"world_cities.quito" = "基多";
"world_cities.montevideo" = "蒙得维的亚";
"world_cities.caracas" = "加拉加斯";
"world_cities.san_juan" = "圣胡安";
"world_cities.panama_city" = "巴拿马城";
"world_cities.reykjavik" = "雷克雅未克";
"game_over" = "游戏结束";
"final_score" = "最终得分";
"play_again" = "再玩一次";
"back_to_menu" = "返回主菜单";
"invalid_category" = "无效类别";
"invalid_category_message" = "未找到所选类别。";
"error" = "错误";
"ok" = "确定";
"world_musicians.beyonce" = "碧昂丝";
"world_musicians.adele" = "阿黛尔";
"world_musicians.taylor_swift" = "泰勒·斯威夫特";
"world_musicians.ed_sheeran" = "艾德·希兰";
"world_musicians.drake" = "德雷克";
"world_musicians.kanye_west" = "坎耶·韦斯特";
"world_musicians.rihanna" = "蕾哈娜";
"world_musicians.bruno_mars" = "布鲁诺·马尔斯";
"world_musicians.justin_bieber" = "贾斯汀·比伯";
"world_musicians.katy_perry" = "凯蒂·佩里";
"world_musicians.lady_gaga" = "Lady Gaga";
"world_musicians.post_malone" = "Post Malone";
"world_musicians.shakira" = "夏奇拉";
"world_musicians.billie_eilish" = "Billie Eilish";
"world_musicians.travis_scott" = "特拉维斯·斯科特";
"world_musicians.cardi_b" = "Cardi B";
"world_musicians.the_weeknd" = "The Weeknd";
"world_musicians.selena_gomez" = "赛琳娜·戈麦斯";
"world_musicians.harry_styles" = "哈里·斯泰尔斯";
"world_musicians.lizzo" = "Lizzo";
"world_musicians.miley_cyrus" = "麦莉·赛勒斯";
"world_musicians.ariana_grande" = "爱莉安娜·格兰德";
"world_musicians.kendrick_lamar" = "肯德里克·拉马尔";
"world_musicians.maroon_5" = "魔力红";
"world_musicians.pink" = "粉红佳人";
"world_musicians.john_legend" = "约翰·传奇";
"world_musicians.coldplay" = "酷玩乐队";
"world_musicians.sia" = "希雅";
"world_musicians.sam_smith" = "萨姆·史密斯";
"world_musicians.dr_dre" = "Dr. Dre";
"world_musicians.lil_nas_x" = "利尔·纳斯·X";
"world_musicians.dua_lipa" = "杜阿·利帕";
"world_musicians.camila_cabello" = "卡米拉·卡贝洛";
"world_musicians.shawn_mendes" = "肖恩·门德斯";
"world_musicians.imagine_dragons" = "梦龙乐队";
"world_musicians.nicki_minaj" = "妮基·米娜";
"world_musicians.migos" = "米戈斯";
"world_musicians.future" = "未来";
"world_musicians.rita_ora" = "丽塔·奥拉";
"world_musicians.jessie_j" = "杰西·J";
"world_musicians.zayn_malik" = "泽恩·马利克";
"world_musicians.kesha" = "凯莎";
"world_musicians.pitbull" = "皮特bull";
"world_musicians.lorde" = "洛德";
"world_musicians.khalid" = "哈立德";
"world_musicians.j_balvin" = "J 巴尔文";
"world_musicians.bad_bunny" = "坏痞兔";
"world_musicians.jhene_aiko" = "婕妮·艾可";
"world_musicians.avicii" = "艾维奇";
"world_musicians.david_guetta" = "大卫·库塔";
"world_musicians.calvin_harris" = "凯尔文·哈里斯";
"world_musicians.marshmello" = "棉花糖";
"world_musicians.alesso" = "艾利索";
"world_musicians.flume" = "弗勒姆";
"world_musicians.kygo" = "凯戈";
"world_musicians.the_chainsmokers" = "烟鬼组合";
"world_musicians.martin_garrix" = "马丁·盖瑞斯";
"world_musicians.travis_barker" = "特拉维斯·巴克";
"world_musicians.bts" = "防弹少年团";
"world_musicians.blackpink" = "BLACKPINK";
"world_musicians.exo" = "EXO";
"world_musicians.twice" = "EXO";
"world_musicians.asap_rocky" = "TWICE";
"world_musicians.lil_wayne" = "TWICE";
"world_musicians.eminem" = "A$AP Rocky";
"world_musicians.jay_z" = "A$AP Rocky";
"world_musicians.lana_del_rey" = "Lil Wayne";
"world_musicians.tame_impala" = "Lil Wayne";
"world_musicians.macklemore" = "Eminem";
"world_musicians.one_direction" = "Eminem";
"world_musicians.marina_diamandis" = "Marina Diamandis";
"world_musicians.the_killers" = "杀手乐队";
"world_musicians.twenty_one_pilots" = "二十一名飞行员";
"world_musicians.arctic_monkeys" = "北极猴子";
"world_musicians.halsey" = "Halsey";
"world_musicians.jack_harlow" = "杰克·哈洛";
"world_musicians.celine_dion" = "席琳·迪翁";
"world_musicians.zedd" = "泽德";
"world_musicians.lauv" = "劳夫";
"world_musicians.troye_sivan" = "特洛耶·希文";
"world_musicians.panic_at_the_disco" = "恐慌乐队!";
"world_musicians.hayley_williams" = "海莉·威廉姆斯";
"world_musicians.skrillex" = "斯克里雷克斯";
"world_musicians.nicky_jam" = "尼基·詹";
"world_musicians.snoop_dogg" = "史努比·狗狗";
"world_musicians.tyler_the_creator" = "泰勒，创作者";
"world_musicians.chris_brown" = "克里斯·布朗";
"world_musicians.fifth_harmony" = "五美组合";
"world_musicians.alicia_keys" = "艾丽西亚·凯斯";
"world_musicians.shaggy" = "夏奇";
"historical_figures.ataturk" = "穆斯塔法·凯末尔·阿塔图尔克";
"historical_figures.napoleon" = "拿破仑·波拿巴";
"historical_figures.einstein" = "阿尔伯特·爱因斯坦";
"historical_figures.newton" = "艾萨克·牛顿";
"historical_figures.da_vinci" = "列奥纳多·达·芬奇";
"historical_figures.caesar" = "朱利叶斯·凯撒";
"historical_figures.cleopatra" = "克利奥帕特拉";
"historical_figures.gandhi" = "圣雄甘地";
"historical_figures.churchill" = "温斯顿·丘吉尔";
"historical_figures.lincoln" = "亚伯拉罕·林肯";
"historical_figures.washington" = "乔治·华盛顿";
"historical_figures.mozart" = "沃尔夫冈·阿马德乌斯·莫扎特";
"historical_figures.beethoven" = "路德维希·凡·贝多芬";
"historical_figures.shakespeare" = "威廉·莎士比亚";
"historical_figures.dante" = "但丁·阿利吉耶里";
"historical_figures.plato" = "柏拉图";
"historical_figures.socrates" = "苏格拉底";
"historical_figures.aristotle" = "亚里士多德";
"historical_figures.confucius" = "孔子";
"historical_figures.buddha" = "佛陀";
"historical_figures.muhammad" = "穆罕默德";
"historical_figures.jesus" = "耶稣";
"historical_figures.moses" = "摩西";
"historical_figures.marco_polo" = "马可·波罗";
"historical_figures.columbus" = "克里斯托弗·哥伦布";
"historical_figures.magellan" = "麦哲伦";
"historical_figures.copernicus" = "哥白尼";
"historical_figures.galileo" = "伽利略·伽利莱";
"historical_figures.kepler" = "约翰内斯·开普勒";
"historical_figures.darwin" = "查尔斯·达尔文";
"historical_figures.freud" = "西格蒙德·弗洛伊德";
"historical_figures.marx" = "卡尔·马克思";
"historical_figures.engels" = "弗里德里希·恩格斯";
"historical_figures.lenin" = "弗拉基米尔·列宁";
"historical_figures.stalin" = "约瑟夫·斯大林";
"historical_figures.hitler" = "阿道夫·希特勒";
"historical_figures.mussolini" = "贝尼托·墨索里尼";
"historical_figures.roosevelt" = "富兰克林·德拉诺·罗斯福";
"historical_figures.truman" = "哈里·S·杜鲁门";
"historical_figures.eisenhower" = "德怀特·D·艾森豪威尔";
"historical_figures.kennedy" = "约翰·F·肯尼迪";
"historical_figures.nixon" = "理查德·尼克松";
"historical_figures.reagan" = "罗纳德·里根";
"historical_figures.thatcher" = "玛格丽特·撒切尔";
"historical_figures.de_gaulle" = "夏尔·戴高乐";
"historical_figures.mandela" = "纳尔逊·曼德拉";
"historical_figures.martin_luther_king" = "马丁·路德·金";
"historical_figures.malcolm_x" = "马尔科姆·X";
"historical_figures.che_guevara" = "切·格瓦拉";
"historical_figures.castro" = "菲德尔·卡斯特罗";
"historical_figures.mao" = "毛泽东";
"historical_figures.gorbachev" = "米哈伊尔·戈尔巴乔夫";
"historical_figures.yeltsin" = "鲍里斯·叶利钦";
"historical_figures.putin" = "弗拉基米尔·普京";
"historical_figures.obama" = "巴拉克·奥巴马";
"historical_figures.trump" = "唐纳德·特朗普";
"historical_figures.biden" = "乔·拜登";
"historical_figures.merkel" = "安格拉·默克尔";
"historical_figures.macron" = "埃马纽埃尔·马克龙";
"historical_figures.johnson" = "鲍里斯·约翰逊";
"historical_figures.sunak" = "里希·苏纳克";
"historical_figures.erdogan" = "雷杰普·塔伊普·埃尔多安";
"historical_figures.king_charles" = "查尔斯国王";
"historical_figures.queen_elizabeth" = "伊丽莎白女王";
"historical_figures.prince_william" = "威廉王子";
"historical_figures.prince_harry" = "哈里王子";
"historical_figures.meghan_markle" = "梅根·马克尔";
"historical_figures.kate_middleton" = "凯特·米德尔顿";
"historical_figures.diana" = "戴安娜王妃";
"historical_figures.philip" = "菲利普亲王";
"historical_figures.anne" = "安妮长公主";
"historical_figures.andrew" = "安德鲁王子";
"historical_figures.edward" = "爱德华王子";
"historical_figures.sophie" = "威塞克斯伯爵夫人苏菲";
"historical_figures.camilla" = "卡米拉，王后配偶";
"historical_figures.catherine" = "凯瑟琳，威尔士王妃";
"historical_figures.william" = "威廉，威尔士亲王";
"historical_figures.george" = "乔治王子";
"historical_figures.charlotte" = "夏洛特公主";
"historical_figures.louis" = "路易王子";
"historical_figures.archie" = "亚 Archie Mountbatten-Windsor";
"historical_figures.lilibet" = "莉莉贝特·蒙巴顿-温莎";
"superheroes.spider_man" = "蜘蛛侠";
"superheroes.iron_man" = "钢铁侠";
"superheroes.captain_america" = "美国队长";
"superheroes.thor" = "雷神";
"superheroes.hulk" = "绿巨人";
"superheroes.black_widow" = "黑寡妇";
"superheroes.hawkeye" = "鹰眼";
"superheroes.doctor_strange" = "奇异博士";
"superheroes.black_panther" = "黑豹";
"superheroes.scarlet_witch" = "红女巫";
"superheroes.vision" = "幻视";
"superheroes.ant_man" = "蚁人";
"superheroes.wasp" = "黄蜂女";
"superheroes.deadpool" = "死侍";
"superheroes.wolverine" = "金刚狼";
"superheroes.cyclops" = "镭射眼";
"superheroes.storm" = "暴风女";
"superheroes.jean_grey" = "琴·格雷";
"superheroes.professor_x" = "X教授";
"superheroes.beast" = "野兽";
"superheroes.gambit" = "牌皇";
"superheroes.rogue" = "罗刹女";
"superheroes.iceman" = "冰人";
"superheroes.colossus" = "钢力士";
"superheroes.nightcrawler" = "夜行者";
"superheroes.silver_surfer" = "银影侠";
"superheroes.daredevil" = "超胆侠";
"superheroes.luke_cage" = "卢克·凯奇";
"superheroes.iron_fist" = "钢铁侠";
"superheroes.ghost_rider" = "恶灵骑士";
"superheroes.moon_knight" = "月光骑士";
"superheroes.shang_chi" = "尚气";
"superheroes.the_punisher" = "惩罚者";
"superheroes.captain_marvel" = "惊奇队长";
"superheroes.nova" = "新星";
"superheroes.star_lord" = "星爵";
"superheroes.gamora" = "卡魔拉";
"superheroes.drax" = "毁灭者";
"superheroes.rocket_raccoon" = "火箭浣熊";
"superheroes.groot" = "格鲁特";
"superheroes.nick_fury" = "尼克·弗瑞";
"superheroes.quicksilver" = "快银";
"superheroes.she_hulk" = "女浩克";
"superheroes.ms_marvel" = "惊奇女士";
"superheroes.sentry" = "哨兵";
"superheroes.blue_marvel" = "蓝色奇迹";
"superheroes.superman" = "超人";
"superheroes.batman" = "蝙蝠侠";
"superheroes.wonder_woman" = "神奇女侠";
"superheroes.the_flash" = "闪电侠";
"superheroes.aquaman" = "海王";
"superheroes.green_lantern" = "绿灯侠";
"superheroes.martian_manhunter" = "火星猎人";
"superheroes.cyborg" = "钢骨";
"superheroes.green_arrow" = "绿箭侠";
"superheroes.black_canary" = "黑金丝雀";
"superheroes.hawkman" = "鹰侠";
"superheroes.hawkgirl" = "鹰女";
"superheroes.shazam" = "Shazam";
"superheroes.zatanna" = "扎坦娜";
"superheroes.john_constantine" = "约翰·康斯坦丁";
"superheroes.doctor_fate" = "命运博士";
"superheroes.raven" = "渡鸦";
"superheroes.starfire" = "星火";
"superheroes.beast_boy" = "野兽小子";
"superheroes.nightwing" = "夜翼";
"superheroes.robin" = "罗宾";
"superheroes.batgirl" = "蝙蝠女";
"superheroes.supergirl" = "超级少女";
"superheroes.red_hood" = "小红帽";
"superheroes.blue_beetle" = "蓝甲虫";
"superheroes.booster_gold" = "金色先锋";
"superheroes.plastic_man" = "塑料人";
"superheroes.the_atom" = "原子侠";
"superheroes.firestorm" = "火风暴";
"superheroes.swamp_thing" = "沼泽怪物";
"superheroes.etrigan_the_demon" = "恶魔埃崔根";
"superheroes.vixen" = "雌狐";
"superheroes.black_lightning" = "黑闪电";
"superheroes.orion" = "猎户座";
"superheroes.big_barda" = "大酒吧";
"superheroes.mister_miracle" = "神奇先生";
"superheroes.spectre" = "幽灵";
"superheroes.doctor_manhattan" = "曼哈顿博士";
"superheroes.rorschach" = "罗夏";
"turkish_celebrities.tarkan" = "塔肯";
"turkish_celebrities.ajda_pekkan" = "阿佳·佩坎";
"turkish_celebrities.asik_veysel" = "阿诗克·韦塞尔";
"turkish_celebrities.baris_manco" = "巴里什·曼乔";
"turkish_celebrities.bulent_ersoy" = "布伦特·埃尔索伊";
"turkish_celebrities.cem_karaca" = "杰姆·卡拉贾";
"turkish_celebrities.edip_akbayram" = "埃迪普·阿克拜拉姆";
"turkish_celebrities.emel_sayin" = "埃梅尔·赛恩";
"turkish_celebrities.erol_evgin" = "埃罗尔·埃夫金";
"turkish_celebrities.fazil_say" = "法泽尔·赛";
"turkish_celebrities.ferdi_tayfur" = "费尔迪·泰福尔";
"turkish_celebrities.gulben_ergen" = "居尔本·埃尔根";
"turkish_celebrities.gulsin_onay" = "居尔辛·奥奈";
"turkish_celebrities.humeyra" = "Hümeyra";
"turkish_celebrities.ibrahim_tatlises" = "İbrahim Tatlıses";
"turkish_celebrities.idil_biret" = "İdil Biret";
"turkish_celebrities.kenan_dogulu" = "Kenan Doğulu";
"turkish_celebrities.mabel_matiz" = "Mabel Matiz";
"turkish_celebrities.mazhar_alanson" = "Mazhar Alanson";
"turkish_celebrities.mfo" = "MFO";
"turkish_celebrities.muslum_gurses" = "Müslüm Gürses";
"turkish_celebrities.nazan_oncel" = "Nazan Öncel";
"turkish_celebrities.neset_ertas" = "Neşet Ertaş";
"turkish_celebrities.nilufer" = "Nilüfer";
"turkish_celebrities.orhan_gencebay" = "奥尔罕·根杰拜";
"turkish_celebrities.sezen_aksu" = "希兹恩·阿克苏";
"turkish_celebrities.sertab_erener" = "瑟塔普·埃雷内尔";
"turkish_celebrities.teoman" = "泰曼";
"turkish_celebrities.yildiz_tilbe" = "耶尔德兹·蒂尔贝";
"turkish_celebrities.zeki_muren" = "泽基·穆伦";
"turkish_celebrities.hande_yener" = "汉黛·耶内尔";
"turkish_celebrities.murat_boz" = "穆拉特·博兹";
"turkish_celebrities.gokhan_turkmen" = "格坎·土库曼";
"turkish_celebrities.sila" = "希拉";
"turkish_celebrities.edis" = "埃迪斯";
"turkish_celebrities.funda_arar" = "丰达·阿拉尔";
"turkish_celebrities.demet_akalin" = "德米特·阿卡林";
"turkish_celebrities.gulsen" = "居尔森";
"turkish_celebrities.mustafa_sandal" = "穆斯塔法·桑达尔";
"turkish_celebrities.gokhan_ozen" = "格科汉·厄曾";
"turkish_celebrities.sebnem_ferah" = "舍布内姆·费拉";
"turkish_celebrities.mor_ve_otesi" = "莫尔与奥特斯";
"turkish_celebrities.haluk_levent" = "哈鲁克·莱文特";
"turkish_celebrities.athena" = "雅典娜";
"turkish_celebrities.manga" = "漫画";
"turkish_celebrities.candan_ercetin" = "坎丹·艾尔切廷";
"turkish_celebrities.levent_yuksel" = "勒文特·于克赛尔";
"turkish_celebrities.cem_adrian" = "杰姆·阿德里安";
"turkish_celebrities.volkan_konak" = "沃尔坎·科纳克";
"turkish_celebrities.sibel_can" = "西贝尔·坎";
"turkish_celebrities.sener_sen" = "舍内尔· Şen";
"turkish_celebrities.kemal_sunal" = "凯末尔·萨纳尔";
"turkish_celebrities.adile_nasit" = "阿迪勒·纳希特";
"turkish_celebrities.munir_ozkul" = "穆尼尔·厄兹库尔";
"turkish_celebrities.halit_ergenc" = "哈利特·埃尔根奇";
"turkish_celebrities.kivanc_tatlitug" = "基万奇·塔特利图";
"turkish_celebrities.kenan_imirzalioglu" = "凯南·伊米尔扎勒奥卢";
"turkish_celebrities.beren_saat" = "贝伦·萨特";
"turkish_celebrities.tuba_buyukustun" = "图芭·布育库斯滕";
"turkish_celebrities.haluk_bilginer" = "哈鲁克·比尔吉纳";
"turkish_celebrities.tarik_akan" = "塔里克·阿坎";
"turkish_celebrities.cuneyt_arkin" = "居内伊特·阿尔孔";
"turkish_celebrities.turkan_soray" = "图尔坎·索拉伊";
"turkish_celebrities.fatma_girik" = "法特玛·吉里克";
"turkish_celebrities.hulya_kocyigit" = "侯利亚·科奇伊伊特";
"turkish_celebrities.filiz_akin" = "菲丽兹·阿克";
"turkish_celebrities.nebahat_cehr" = "内巴哈特·切赫雷";
"turkish_celebrities.yilmaz_erdogan" = "耶尔马兹·埃尔多安";
"turkish_celebrities.demet_evgar" = "代梅特·埃夫加";
"turkish_celebrities.ata_demirer" = "阿塔·德米尔尔";
"turkish_celebrities.gulse_birsel" = "居尔瑟·比尔塞尔";
"turkish_celebrities.engin_akyurek" = "埃尔金·阿克尤雷克";
"turkish_celebrities.berguzar_korel" = "贝尔古扎尔·科雷尔";
"turkish_celebrities.burak_ozcivit" = "布拉克·厄兹奇维特";
"turkish_celebrities.baris_arduc" = "巴里什·阿尔杜奇";
"turkish_celebrities.serenay_sarikaya" = "瑟拉奈·萨勒卡亚";
"turkish_celebrities.halil_ergun" = "哈利勒·埃尔根";
"turkish_celebrities.fikret_kuskan" = "菲克雷特·库尚";
"turkish_celebrities.erdal_besikcioglu" = "埃尔达尔·贝西克奇奥卢";
"turkish_celebrities.riza_kocaoglu" = "勒扎·科贾奥卢";
"turkish_celebrities.ugur_yucel" = "乌乌尔·于贾尔";
"turkish_celebrities.meltem_cumbul" = "梅尔特姆·詹布尔";
"turkish_celebrities.nurgul_yesilcay" = "努尔居尔·耶希尔恰伊";
"turkish_celebrities.ozge_ozpirinci" = "Özge Özpirinçci";
"turkish_celebrities.cansu_dere" = "Cansu Dere";
"turkish_celebrities.tamer_karadagli" = "塔梅尔·卡拉达厄";
"turkish_celebrities.selcuk_yontem" = "塞尔丘克· method";
"turkish_celebrities.okan_bayulgen" = "奥坎·巴尤尔根";
"turkish_celebrities.gurgen_oz" = "Gürgen Öz";
"turkish_celebrities.ismail_hacioglu" = "伊斯梅尔·哈吉奥卢";
"turkish_celebrities.cagatay_ulusoy" = "恰合台·乌鲁索伊";
"turkish_celebrities.farah_zeynep_abdullah" = "法拉·泽伊内普·阿卜杜拉";
"turkish_celebrities.funda_eryigit" = "Funda Eryiğit";
"turkish_celebrities.mehmet_gunsur" = "Mehmet Günsür";
"turkish_celebrities.yetkin_dikinciler" = "叶廷·迪金吉勒";
"turkish_celebrities.ahmet_mumtaz_taylan" = "艾哈迈德·穆姆塔兹·泰兰";
"turkish_celebrities.sumru_yavrucuk" = "苏姆鲁·亚武鲁克";
"turkish_celebrities.cem_yilmaz" = "杰姆·伊尔马兹";
"turkish_celebrities.tolga_cevik" = "托尔加·切维克";
"turkish_celebrities.yilmaz_guney" = "伊尔马兹·居内伊";
"turkish_cuisine.kebap" = "烤肉";
"turkish_cuisine.doner" = "土耳其烤肉";
"turkish_cuisine.iskender" = "伊斯坎德尔烤肉";
"turkish_cuisine.adana_kebabi" = "阿达纳烤肉";
"turkish_cuisine.urfa_kebabi" = "乌尔法烤肉";
"turkish_cuisine.lahmacun" = "土耳其披萨";
"turkish_cuisine.pide" = "土耳其馅饼";
"turkish_cuisine.borek" = "博雷克";
"turkish_cuisine.manti" = "土耳其饺子";
"turkish_cuisine.cig_kofte" = "生肉丸";
"turkish_cuisine.karniyarik" = "酿茄子";
"turkish_cuisine.hunkar_begendi" = "苏丹烤茄子";
"turkish_cuisine.imam_bayildi" = "伊玛目晕倒";
"turkish_cuisine.zeytinyagli_yaprak_sarma" = "橄榄油酿葡萄叶";
"turkish_cuisine.dolma" = "多尔玛（蔬菜酿米）";
"turkish_cuisine.kuru_fasulye" = "干豆";
"turkish_cuisine.pilav" = "米饭";
"turkish_cuisine.mercimek_corbasi" = "扁豆汤";
"turkish_cuisine.ezogelin_corbasi" = "埃佐盖林汤（土耳其风味汤）";
"turkish_cuisine.tarhana_corbasi" = "塔拉纳汤（土耳其发酵汤）";
"turkish_cuisine.tavuklu_pilav" = "鸡肉饭";
"turkish_cuisine.balik_ekmek" = "鱼肉面包";
"turkish_cuisine.menemen" = "门内门（土耳其鸡蛋蔬菜）";
"turkish_cuisine.sucuklu_yumurta" = "香肠鸡蛋";
"turkish_cuisine.gozleme" = " gözleme（土耳其薄饼）";
"turkish_cuisine.kasarli_tost" = "芝士吐司";
"turkish_cuisine.midye_dolma" = "海虹酿饭";
"turkish_cuisine.kokorec" = "烤羊肠";
"turkish_cuisine.paca_corbasi" = "羊蹄汤";
"turkish_cuisine.iskembe_corbasi" = "牛肚汤";
"turkish_cuisine.tandir_kebabi" = "坦德尔烤肉";
"turkish_cuisine.cag_kebabi" = " çağ 烤肉";
"turkish_cuisine.testi_kebabi" = "陶罐烤肉";
"turkish_cuisine.ali_nazik" = "阿里纳泽克";
"turkish_cuisine.beyti_kebabi" = "贝伊提烤肉";
"turkish_cuisine.patlican_kebabi" = "茄子烤肉";
"turkish_cuisine.cizlak_kebabi" = "奇兹勒克烤肉";
"turkish_cuisine.kagit_kebabi" = "纸包烤肉";
"turkish_cuisine.ciger_sis" = "肝肉串";
"turkish_cuisine.perde_pilavi" = "幕帘抓饭";
"turkish_cuisine.hamsili_pilav" = "凤尾鱼抓饭";
"turkish_cuisine.tepsi_kebabi" = "烤盘烤肉";
"turkish_cuisine.kapuska" = "包菜炖菜";
"turkish_cuisine.icli_kofte" = "İçli Köfte";
"turkish_cuisine.arnavut_cigeri" = "阿尔巴尼亚肝";
"turkish_cuisine.tirit" = "提里特";
"turkish_cuisine.keskek" = "凯什凯克";
"turkish_cuisine.mihlama" = "米赫拉马";
"turkish_cuisine.kuzu_guvec" = "烤羊肉砂锅";
"turkish_cuisine.saksuka" = "萨克苏卡";
"turkish_cuisine.zeytinyagli_enginar" = "橄榄油朝鲜蓟";
"turkish_cuisine.taze_fasulye" = "鲜菜豆";
"turkish_cuisine.kabak_cicegi_dolmasi" = "西葫芦花酿肉";
"turkish_cuisine.pazi_sarmasi" = "甜菜叶卷";
"turkish_cuisine.baklava" = "果仁蜜饼";
"turkish_cuisine.kunefe" = "库纳法";
"turkish_cuisine.kadayif" = "卡达伊夫";
"turkish_cuisine.revani" = "瑞瓦尼";
"turkish_cuisine.sekerpare" = "谢克帕雷";
"turkish_cuisine.kazandibi" = "烤鸡胸布丁";
"turkish_cuisine.sutlac" = "米布丁";
"turkish_cuisine.asure" = "阿舒拉";
"turkish_cuisine.lokum" = "土耳其软糖";
"turkish_cuisine.helva" = "海瓦";
"turkish_cuisine.tahin_pekmez" = "芝麻酱蜜";
"turkish_cuisine.cevizli_sucuk" = "核桃果脯";
"turkish_cuisine.zerde" = "泽尔德";
"turkish_cuisine.irmik_helvasi" = "粗麦粉甜点";
"turkish_cuisine.trilece" = "提拉米苏";
"turkish_cuisine.firin_sutlac" = "烤米布丁";
"turkish_cuisine.tavukgogsu" = "鸡丝";
"turkish_cuisine.kaymak" = "凝脂";
"turkish_cuisine.ayva_tatlisi" = "榅桲甜点";
"turkish_cuisine.gullac" = "古拉奇";
"turkish_cuisine.ekmek_kadayifi" = "烤面包甜点";
"turkish_cuisine.yogurt" = "酸奶";
"turkish_cuisine.ayran" = "艾兰";
"turkish_cuisine.salgam" = "萝卜汁";
"turkish_cuisine.boza" = "博扎";
"turkish_cuisine.turk_kahvesi" = "土耳其咖啡";
"turkish_cuisine.cay" = "茶";
"turkish_cuisine.salep" = "兰茎粉";
"turkish_cuisine.kusburnu_cayi" = "玫瑰果茶";
"turkish_cuisine.osmanli_serbetleri" = "奥斯曼果汁";
"world_actors.leonardo_dicaprio" = "莱昂纳多·迪卡普里奥";
"world_actors.scarlett_johansson" = "斯嘉丽·约翰逊";
"world_actors.dwayne_johnson" = "道恩·强森";
"world_actors.brad_pitt" = "布拉德·皮特";
"world_actors.johnny_depp" = "约翰尼·德普";
"world_actors.emma_stone" = "艾玛·斯通";
"world_actors.angelina_jolie" = "安吉丽娜·朱莉";
"world_actors.robert_downey_jr" = "小罗伯特·唐尼";
"world_actors.chris_hemsworth" = "克里斯·海姆斯沃斯";
"world_actors.tom_hanks" = "汤姆·汉克斯";
"world_actors.meryl_streep" = "梅丽尔·斯特里普";
"world_actors.will_smith" = "威尔·史密斯";
"world_actors.jennifer_lawrence" = "詹妮弗·劳伦斯";
"world_actors.keanu_reeves" = "基努·里维斯";
"world_actors.tom_cruise" = "汤姆·克鲁斯";
"world_actors.zendaya" = "赞达亚";
"world_actors.ryan_reynolds" = "瑞恩·雷诺兹";
"world_actors.emma_watson" = "艾玛·沃特森";
"world_actors.chris_evans" = "克里斯·埃文斯";
"world_actors.gal_gadot" = "盖尔·加朵";
"world_actors.samuel_l_jackson" = "塞缪尔·杰克逊";
"world_actors.morgan_freeman" = "摩根·弗里曼";
"world_actors.julia_roberts" = "茱莉亚·罗伯茨";
"world_actors.nicole_kidman" = "妮可·基德曼";
"world_actors.matthew_mcconaughey" = "马修·麦康纳";
"world_actors.charlize_theron" = "查理兹·塞隆";
"world_actors.denzel_washington" = "丹泽尔·华盛顿";
"world_actors.matt_damon" = "马特·达蒙";
"world_actors.cate_blanchett" = "凯特·布兰切特";
"world_actors.margot_robbie" = "玛格特·罗比";
"world_actors.jessica_chastain" = "杰西卡·查斯坦";
"world_actors.tom_hardy" = "汤姆·哈迪";
"world_actors.alicia_vikander" = "艾丽西亚·维坎德";
"world_actors.natalie_portman" = "娜塔莉·波特曼";
"world_actors.michael_fassbender" = "迈克尔·法斯宾德";
"world_actors.saoirse_ronan" = "西尔莎·罗南";
"world_actors.reese_witherspoon" = "瑞茜·威瑟斯彭";
"world_actors.jared_leto" = "杰瑞德·莱托";
"world_actors.anne_hathaway" = "安妮·海瑟薇";
"world_actors.paul_rudd" = "保罗·路德";
"world_actors.chadwick_boseman" = "查德维克·博斯曼";
"world_actors.jason_momoa" = "杰森·莫玛";
"world_actors.adam_driver" = "亚当·德赖弗";
"world_actors.rami_malek" = "拉米·马雷克";
"world_actors.tessa_thompson" = "泰莎·汤普森";
"world_actors.sophie_turner" = "索菲·特纳";
"world_actors.henry_cavill" = "亨利·卡维尔";
"world_actors.keira_knightley" = "凯拉·奈特莉";
"world_actors.will_ferrell" = "威尔·法瑞尔";
"world_actors.ben_affleck" = "本·阿弗莱克";
"world_actors.matt_smith" = "马特·史密斯";
"world_actors.caitriona_balfe" = "凯瑞奥娜·巴尔夫";
"world_actors.kerry_washington" = "凯丽·华盛顿";
"world_actors.viola_davis" = "维奥拉·戴维斯";
"world_actors.mark_ruffalo" = "马克·鲁法洛";
"world_actors.amy_adams" = "艾米·亚当斯";
"world_actors.rachel_mcadams" = "瑞秋·麦克亚当斯";
"world_actors.shailene_woodley" = "谢琳·伍德蕾";
"world_actors.paul_walker" = "保罗·沃克";
"world_actors.john_boyega" = "约翰·博耶加";
"world_actors.ethan_hawke" = "伊桑·霍克";
"world_actors.idris_elba" = "伊德瑞斯·艾尔巴";
"world_actors.tina_fey" = "蒂娜·菲";
"world_actors.steve_carell" = "史蒂夫·卡瑞尔";
"world_actors.ryan_gosling" = "瑞恩·高斯林";
"world_actors.bryan_cranston" = "布莱恩·科兰斯顿";
"world_actors.harrison_ford" = "哈里森·福特";
"world_actors.daniel_craig" = "丹尼尔·克雷格";
"world_actors.jeremy_renner" = "杰瑞米·雷纳";
"world_actors.gina_rodriguez" = "吉娜·罗德里格斯";
"world_actors.mindy_kaling" = "敏迪·卡灵";
"world_actors.benedict_cumberbatch" = "本尼迪克特·康伯巴奇";
"world_actors.chris_pratt" = "克里斯·普拉特";
"world_actors.priyanka_chopra" = "朴雅卡·乔普拉";
"world_actors.keegan_michael_key" = "基根-迈克尔·凯";
"world_actors.mandy_moore" = "曼迪·摩尔";
"world_actors.hugh_jackman" = "休·杰克曼";
"world_actors.julianne_moore" = "朱丽安·摩尔";
"world_actors.anya_taylor_joy" = "安雅·泰勒-乔伊";
"world_actors.tom_holland" = "汤姆·霍兰德";
"world_actors.emma_roberts" = "艾玛·罗伯茨";
"world_actors.zoe_saldana" = "佐伊·索尔达娜";
"world_actors.rachael_leigh_cook" = "瑞秋·蕾·库克";
"world_actors.jesse_eisenberg" = "杰西·艾森伯格";
"world_actors.michelle_williams" = "米歇尔·威廉姆斯";
"world_actors.andrew_garfield" = "安德鲁·加菲尔德";
"world_actors.chris_pine" = "克里斯·派恩";
"world_actors.channing_tatum" = "查宁·塔图姆";
"world_actors.tiffany_haddish" = "蒂凡尼·哈迪什";
"world_actors.lupita_nyongo" = "露皮塔·尼永奥";
"world_actors.jason_statham" = "杰森·斯坦森";
"world_actors.lily_james" = "莉莉·詹姆斯";
"world_actors.charlie_hunnam" = "查理·汉纳姆";
"world_actors.evan_peters" = "伊万·彼得斯";
"world_actors.tom_felton" = "汤姆·费尔顿";
"world_actors.jessica_biel" = "杰西卡·贝尔";
"world_cities.new_york" = "纽约";
"world_cities.los_angeles" = "洛杉矶";
"world_cities.london" = "伦敦";
"world_cities.paris" = "巴黎";
"world_cities.rome" = "罗马";
"world_cities.berlin" = "柏林";
"world_cities.madrid" = "马德里";
"world_cities.barcelona" = "巴塞罗那";
"world_cities.istanbul" = "伊斯坦布尔";
"world_cities.dubai" = "迪拜";
"world_cities.tokyo" = "东京";
"world_cities.kyoto" = "京都";
"world_cities.beijing" = "北京";
"world_cities.shanghai" = "上海";
"world_cities.hong_kong" = "香港";
"world_cities.bangkok" = "曼谷";
"world_cities.singapore" = "新加坡";
"world_cities.seoul" = "首尔";
"world_cities.moscow" = "莫斯科";
"world_cities.st_petersburg" = "圣彼得堡";
"world_cities.sydney" = "悉尼";
"world_cities.melbourne" = "墨尔本";
"world_cities.rio_de_janeiro" = "里约热内卢";
"world_cities.sao_paulo" = "圣保罗";
"world_cities.buenos_aires" = "布宜诺斯艾利斯";
"world_cities.mexico_city" = "墨西哥城";
"world_cities.toronto" = "多伦多";
"world_cities.vancouver" = "温哥华";
"world_cities.chicago" = "芝加哥";
"world_cities.san_francisco" = "旧金山";
"world_cities.washington_dc" = "华盛顿特区";
"world_cities.las_vegas" = "拉斯维加斯";
"world_cities.miami" = "迈阿密";
"world_cities.boston" = "波士顿";
"world_cities.amsterdam" = "阿姆斯特丹";
"world_cities.brussels" = "布鲁塞尔";
"world_cities.vienna" = "维也纳";
"world_cities.prague" = "布拉格";
"world_cities.budapest" = "布达佩斯";
"world_cities.copenhagen" = "哥本哈根";
"world_cities.stockholm" = "斯德哥尔摩";
"world_cities.oslo" = "奥斯陆";
"world_cities.helsinki" = "赫尔辛基";
"world_cities.dublin" = "都柏林";
"world_cities.athens" = "雅典";
"world_cities.warsaw" = "华沙";
"world_cities.lisbon" = "里斯本";
"world_cities.venice" = "威尼斯";
"world_cities.milan" = "米兰";
"world_cities.florence" = "佛罗伦萨";
"world_cities.naples" = "那不勒斯";
"world_cities.zurich" = "苏黎世";
"world_cities.geneva" = "日内瓦";
"world_cities.munich" = "慕尼黑";
"world_cities.frankfurt" = "法兰克福";
"world_cities.hamburg" = "汉堡";
"world_cities.johannesburg" = "约翰内斯堡";
"world_cities.cape_town" = "开普敦";
"world_cities.casablanca" = "卡萨布兰卡";
"world_cities.marrakech" = "马拉喀什";
"world_cities.ankara" = "安卡拉";
"world_cities.jerusalem" = "耶路撒冷";
"world_cities.cairo" = "开罗";
"world_cities.alexandria" = "亚历山大港";
"world_cities.new_delhi" = "新德里";
"world_cities.mumbai" = "孟买";
"world_cities.bangalore" = "班加罗尔";
"world_cities.jakarta" = "雅加达";
"world_cities.kuala_lumpur" = "吉隆坡";
"world_cities.ho_chi_minh_city" = "胡志明市";
"world_cities.hanoi" = "河内";
"world_cities.manila" = "马尼拉";
"world_cities.taipei" = "台北";
"world_cities.doha" = "多哈";
"world_cities.abu_dhabi" = "阿布扎比";
"world_cities.riyadh" = "利雅得";
"world_cities.mecca" = "麦加";
"world_cities.medina" = "麦地那";
"world_cities.tehran" = "德黑兰";
"world_cities.baghdad" = "巴格达";
"world_cities.karachi" = "卡拉奇";
"world_cities.lagos" = "拉各斯";
"world_cities.nairobi" = "内罗毕";
"world_cities.accra" = "阿克拉";
"world_cities.perth" = "珀斯";
"world_cities.brisbane" = "布里斯班";
"world_cities.auckland" = "奥克兰";
"world_cities.wellington" = "惠灵顿";
"world_cities.kathmandu" = "加德满都";
"world_cities.havana" = "哈瓦那";
"world_cities.santiago" = "圣地亚哥";
"world_cities.bogota" = "波哥大";
"world_cities.lima" = "利马";
"world_cities.quito" = "基多";
"world_cities.montevideo" = "蒙得维的亚";
"world_cities.caracas" = "加拉加斯";
"world_cities.san_juan" = "圣胡安";
"world_cities.panama_city" = "巴拿马";
"world_cities.reykjavik" = "雷克雅未克";
"world_cuisine.pizza" = "披萨";
"world_cuisine.pasta_carbonara" = "意式培根蛋面";
"world_cuisine.lasagna" = "千层面";
"world_cuisine.risotto" = "意大利烩饭";
"world_cuisine.tiramisu" = "提拉米苏";
"world_cuisine.bruschetta" = "意式烤面包";
"world_cuisine.gelato" = "意式冰淇淋";
"world_cuisine.croissant" = "可颂";
"world_cuisine.coq_au_vin" = "红酒焖鸡";
"world_cuisine.ratatouille" = "普罗旺斯炖菜";
"world_cuisine.creme_brulee" = "焦糖布丁";
"world_cuisine.bouillabaisse" = "马赛鱼汤";
"world_cuisine.french_onion_soup" = "法式洋葱汤";
"world_cuisine.macaron" = "马卡龙";
"world_cuisine.burger" = "汉堡";
"world_cuisine.hot_dog" = "热狗";
"world_cuisine.bbq_ribs" = "烤猪肋排";
"world_cuisine.buffalo_wings" = "水牛城鸡翅";
"world_cuisine.apple_pie" = "苹果派";
"world_cuisine.mac_and_cheese" = "通心粉和奶酪";
"world_cuisine.cheesecake" = "芝士蛋糕";
"world_cuisine.peking_duck" = "北京烤鸭";
"world_cuisine.sweet_and_sour_pork" = "糖醋里脊";
"world_cuisine.dumplings" = "饺子";
"world_cuisine.spring_rolls" = "春卷";
"world_cuisine.kung_pao_chicken" = "宫保鸡丁";
"world_cuisine.fried_rice" = "炒饭";
"world_cuisine.hot_pot" = "火锅";
"world_cuisine.sushi" = "寿司";
"world_cuisine.ramen" = "拉面";
"world_cuisine.tempura" = "天妇罗";
"world_cuisine.yakitori" = "烤鸡肉串";
"world_cuisine.miso_soup" = "味噌汤";
"world_cuisine.takoyaki" = "章鱼小丸子";
"world_cuisine.okonomiyaki" = "大阪烧";
"world_cuisine.chicken_tikka_masala" = "奶油咖喱鸡";
"world_cuisine.biryani" = "印度香饭";
"world_cuisine.naan" = "馕";
"world_cuisine.dal_tadka" = "黄油扁豆";
"world_cuisine.butter_chicken" = "黄油鸡";
"world_cuisine.samosa" = "萨摩萨";
"world_cuisine.chana_masala" = "鹰嘴豆咖喱";
"world_cuisine.paella" = "西班牙海鲜饭";
"world_cuisine.tapas" = "西班牙小吃";
"world_cuisine.gazpacho" = "西班牙凉菜汤";
"world_cuisine.churros" = "吉事果";
"world_cuisine.tortilla_espanola" = "西班牙土豆煎饼";
"world_cuisine.jamon_iberico" = "伊比利亚火腿";
"world_cuisine.pisto" = "普斯托";
"world_cuisine.tacos" = "塔可";
"world_cuisine.burrito" = "墨西哥卷饼";
"world_cuisine.quesadilla" = "墨西哥馅饼";
"world_cuisine.guacamole" = "鳄梨酱";
"world_cuisine.chili_con_carne" = "辣椒牛肉";
"world_cuisine.tamales" = "塔马利";
"world_cuisine.moussaka" = "穆萨卡";
"world_cuisine.souvlaki" = "希腊烤肉串";
"world_cuisine.tzatziki" = "酸奶黄瓜";
"world_cuisine.dolmades" = "希腊葡萄叶卷";
"world_cuisine.greek_salad" = "希腊沙拉";
"world_cuisine.spanakopita" = "菠菜派";
"world_cuisine.pad_thai" = "泰式炒粉";
"world_cuisine.green_curry" = "绿咖喱";
"world_cuisine.tom_yum_soup" = "冬荫功汤";
"world_cuisine.massaman_curry" = "马萨曼咖喱";
"world_cuisine.mango_sticky_rice" = "芒果糯米饭";
"world_cuisine.som_tam" = "青木瓜沙拉";
"world_cuisine.satay" = "沙爹";
"world_cuisine.kimchi" = "泡菜";
"world_cuisine.bibimbap" = "石锅拌饭";
"world_cuisine.bulgogi" = "烤肉";
"world_cuisine.tteokbokki" = "辣炒年糕";
"world_cuisine.japchae" = "杂菜";
"world_cuisine.samgyeopsal" = "烤五花肉";
"world_cuisine.kimchi_jjigae" = "泡菜汤";
"world_cuisine.hummus" = "鹰嘴豆泥";
"world_cuisine.falafel" = "炸豆丸子";
"world_cuisine.shawarma" = "沙瓦玛";
"world_cuisine.baba_ganoush" = "茄泥";
"world_cuisine.tabbouleh" = "塔布勒沙拉";
"world_cuisine.knafeh" = "库纳法";
"world_cuisine.mansaf" = "曼萨夫";
"world_cuisine.bratwurst" = "香肠";
"world_cuisine.sauerbraten" = "德国酸牛肉";
"world_cuisine.pretzel" = "椒盐卷饼";
"world_cuisine.schnitzel" = "炸肉排";
"world_cuisine.kartoffelsalat" = "土豆沙拉";
"world_cuisine.currywurst" = "咖喱香肠";
"world_cuisine.apfelstrudel" = "苹果卷";
"world_cuisine.feijoada" = "巴西黑豆炖肉";
"world_cuisine.pao_de_queijo" = "奶酪面包";
"world_cuisine.brigadeiro" = "巧克力球";
"world_cuisine.acai_bowl" = "巴西莓碗";
"world_cuisine.moqueca" = "莫克卡鱼汤";
"world_cuisine.churrasco" = "巴西烤肉";
"world_cuisine.coxinha" = "炸鸡肉球";
"historical_figures.albert_einstein" = "阿尔伯特·爱因斯坦";
"historical_figures.martin_luther_king" = "马丁·路德·金";
"historical_figures.mahatma_gandhi" = "圣雄甘地";
"historical_figures.nelson_mandela" = "纳尔逊·曼德拉";
"historical_figures.winston_churchill" = "温斯顿·丘吉尔";
"historical_figures.franklin_roosevelt" = "富兰克林·罗斯福";
"historical_figures.john_f_kennedy" = "约翰·肯尼迪";
"historical_figures.margaret_thatcher" = "玛格丽特·撒切尔";
"historical_figures.queen_elizabeth_ii" = "伊丽莎白二世女王";
"historical_figures.charles_de_gaulle" = "夏尔·戴高乐";
"historical_figures.adolf_hitler" = "阿道夫·希特勒";
"historical_figures.joseph_stalin" = "约瑟夫·斯大林";
"historical_figures.mao_zedong" = "毛泽东";
"historical_figures.fidel_castro" = "菲德尔·卡斯特罗";
"historical_figures.che_guevara" = "切·格瓦拉";
"historical_figures.mother_teresa" = "特蕾莎修女";
"historical_figures.pope_john_paul_ii" = "约翰·保罗二世";
"historical_figures.dalai_lama" = "达赖喇嘛";
"historical_figures.malcolm_x" = "马尔科姆·X";
"historical_figures.rosa_parks" = "罗莎·帕克斯";
"historical_figures.harriet_tubman" = "哈丽雅特·塔布曼";
"historical_figures.susan_b_anthony" = "苏珊·B·安东尼";
"historical_figures.emmeline_pankhurst" = "埃米琳·潘克赫斯特";
"historical_figures.marie_curie" = "玛丽·居里";
"historical_figures.isaac_newton" = "艾萨克·牛顿";
"historical_figures.charles_darwin" = "查尔斯·达尔文";
"historical_figures.galileo_galilei" = "伽利略·伽利莱";
"historical_figures.nikola_tesla" = "尼古拉·特斯拉";
"historical_figures.thomas_edison" = "托马斯·爱迪生";
"historical_figures.alexander_graham_bell" = "亚历山大·格雷厄姆·贝尔";
"historical_figures.henry_ford" = "亨利·福特";
"historical_figures.walt_disney" = "沃尔特·迪士尼";
"historical_figures.pablo_picasso" = "巴勃罗·毕加索";
"historical_figures.vincent_van_gogh" = "文森特·梵高";
"historical_figures.leonardo_da_vinci" = "列奥纳多·达·芬奇";
"historical_figures.michelangelo" = "米开朗基罗";
"historical_figures.william_shakespeare" = "威廉·莎士比亚";
"historical_figures.jane_austen" = "简·奥斯汀";
"historical_figures.charles_dickens" = "查尔斯·狄更斯";
"historical_figures.mark_twain" = "马克·吐温";
"historical_figures.ernest_hemingway" = "欧内斯特·海明威";
"historical_figures.f_scott_fitzgerald" = "F·斯科特·菲茨杰拉德";
"historical_figures.george_orwell" = "乔治·奥威尔";
"historical_figures.virginia_woolf" = "弗吉尼亚·伍尔夫";
"historical_figures.maya_angelou" = "玛雅·安吉洛";
"historical_figures.toni_morrison" = "托妮·莫里森";
"historical_figures.gabriel_garcia_marquez" = "加夫列尔·加西亚·马尔克斯";
"historical_figures.pablo_neruda" = "巴勃罗·聂鲁达";
"historical_figures.federico_garcia_lorca" = "费德里科·加西亚·洛尔卡";
"historical_figures.oscar_wilde" = "奥斯卡·王尔德";
"historical_figures.james_joyce" = "詹姆斯·乔伊斯";
"historical_figures.franz_kafka" = "弗兰兹·卡夫卡";
"historical_figures.marcel_proust" = "马塞尔·普鲁斯特";
"historical_figures.victor_hugo" = "维克多·雨果";
"historical_figures.alexandre_dumas" = "亚历山大·仲马";
"historical_figures.jules_verne" = "儒勒·凡尔纳";
"historical_figures.hans_christian_andersen" = "汉斯·克里斯蒂安·安徒生";
"historical_figures.brothers_grimm" = "格林兄弟";
"historical_figures.lewis_carroll" = "刘易斯·卡罗尔";
"historical_figures.j_r_r_tolkien" = "J.R.R.托尔金";
"historical_figures.c_s_lewis" = "C.S.刘易斯";
"historical_figures.roald_dahl" = "罗尔德·达尔";
"historical_figures.dr_seuss" = "苏斯博士";
"historical_figures.beatrix_potter" = "比阿特丽克斯·波特";
"historical_figures.agatha_christie" = "阿加莎·克里斯蒂";
"historical_figures.arthur_conan_doyle" = "亚瑟·柯南·道尔";
"historical_figures.edgar_allan_poe" = "埃德加·爱伦·坡";
"historical_figures.h_p_lovecraft" = "H.P.洛夫克拉夫特";
"historical_figures.stephen_king" = "斯蒂芬·金";
"historical_figures.j_k_rowling" = "J.K.罗琳";
"historical_figures.george_r_r_martin" = "乔治·R·R·马丁";
"historical_figures.dan_brown" = "丹·布朗";
"historical_figures.john_grisham" = "约翰·格里沙姆";
"historical_figures.michael_crichton" = "迈克尔·克莱顿";
"historical_figures.tom_clancy" = "汤姆·克兰西";
"historical_figures.ken_follett" = "肯·福莱特";
"historical_figures.umberto_eco" = "翁贝托·艾柯";
"historical_figures.milan_kundera" = "米兰·昆德拉";
"historical_figures.paulo_coelho" = "保罗·柯艾略";
"historical_figures.isabel_allende" = "伊莎贝尔·阿连德";
"historical_figures.jorge_luis_borges" = "豪尔赫·路易斯·博尔赫斯";
"historical_figures.julio_cortazar" = "胡里奥·科塔萨尔";
"historical_figures.mario_vargas_llosa" = "马里奥·巴尔加斯·略萨";
"historical_figures.carlos_fuentes" = "卡洛斯·富恩特斯";
"historical_figures.octavio_paz" = "奥克塔维奥·帕斯";
"historical_figures.pablo_neruda" = "巴勃罗·聂鲁达";
"historical_figures.gabriel_garcia_marquez" = "加夫列尔·加西亚·马尔克斯";
"historical_figures.federico_garcia_lorca" = "费德里科·加西亚·洛尔迦";
"historical_figures.oscar_wilde" = "奥斯卡·王尔德";
"historical_figures.james_joyce" = "詹姆斯·乔伊斯";
"historical_figures.franz_kafka" = "弗兰兹·卡夫卡";
"historical_figures.marcel_proust" = "马塞尔·普鲁斯特";
"historical_figures.victor_hugo" = "维克多·雨果";
"historical_figures.alexandre_dumas" = "亚历山大·仲马";
"historical_figures.jules_verne" = "儒勒·凡尔纳";
"historical_figures.hans_christian_andersen" = "汉斯·克里斯蒂安·安徒生";
"historical_figures.brothers_grimm" = "格林兄弟";
"historical_figures.lewis_carroll" = "刘易斯·卡罗尔";
"historical_figures.j_r_r_tolkien" = "J.R.R. 托尔金";
"historical_figures.c_s_lewis" = "C.S. 刘易斯";
"historical_figures.roald_dahl" = "罗尔德·达尔";
"historical_figures.dr_seuss" = "苏斯博士";
"historical_figures.beatrix_potter" = "碧翠丝·波特";
"world_cuisine.coxinha" = "可乐饼";
"animals" = "动物";
"animals.dog" = "狗";
"animals.cat" = "猫";
"animals.hamster" = "仓鼠";
"animals.rabbit" = "兔子";
"animals.parrot" = "鹦鹉";
"animals.goldfish" = "金鱼";
"animals.guinea_pig" = "豚鼠";
"animals.lion" = "狮子";
"animals.tiger" = "老虎";
"animals.elephant" = "大象";
"animals.giraffe" = "长颈鹿";
"animals.zebra" = "斑马";
"animals.monkey" = "猴子";
"animals.gorilla" = "大猩猩";
"animals.cheetah" = "猎豹";
"animals.rhinoceros" = "犀牛";
"animals.hippopotamus" = "河马";
"animals.dolphin" = "海豚";
"animals.whale" = "鲸鱼";
"animals.shark" = "鲨鱼";
"animals.octopus" = "章鱼";
"animals.penguin" = "企鹅";
"animals.seahorse" = "海马";
"animals.jellyfish" = "水母";
"animals.eagle" = "鹰";
"animals.owl" = "猫头鹰";
"animals.sparrow" = "麻雀";
"animals.crow" = "乌鸦";
"animals.peacock" = "孔雀";
"animals.flamingo" = "火烈鸟";
"animals.toucan" = "巨嘴鸟";
"animals.snake" = "蛇";
"animals.crocodile" = "鳄鱼";
"animals.turtle" = "乌龟";
"animals.lizard" = "蜥蜴";
"animals.chameleon" = "变色龙";
"animals.iguana" = "鬣蜥";
"animals.butterfly" = "蝴蝶";
"animals.bee" = "蜜蜂";
"animals.ant" = "蚂蚁";
"animals.spider" = "蜘蛛";
"animals.grasshopper" = "蚱蜢";
"animals.ladybug" = "瓢虫";
"animals.cow" = "母牛";
"animals.sheep" = "绵羊";
"animals.goat" = "山羊";
"animals.pig" = "猪";
"animals.horse" = "马";
"animals.chicken" = "鸡";
"animals.duck" = "鸭";
"tech_brands.apple" = "Apple";
"tech_brands.google" = "Google";
"tech_brands.microsoft" = "Microsoft";
"tech_brands.amazon" = "Amazon";
"tech_brands.meta" = "Meta";
"tech_brands.tesla" = "Tesla";
"tech_brands.samsung" = "Samsung";
"tech_brands.huawei" = "Huawei";
"tech_brands.xiaomi" = "Xiaomi";
"tech_brands.sony" = "Sony";
"tech_brands.instagram" = "Instagram";
"tech_brands.tiktok" = "TikTok";
"tech_brands.youtube" = "YouTube";
"tech_brands.twitter" = "Twitter";
"tech_brands.linkedin" = "LinkedIn";
"tech_brands.snapchat" = "Snapchat";
"tech_brands.pinterest" = "Pinterest";
"tech_brands.reddit" = "Reddit";
"tech_brands.discord" = "Discord";
"tech_brands.telegram" = "Telegram";
"tech_brands.netflix" = "Netflix";
"tech_brands.spotify" = "Spotify";
"tech_brands.disney_plus" = "Disney Plus";
"tech_brands.amazon_prime" = "Amazon Prime";
"tech_brands.hulu" = "Hulu";
"tech_brands.twitch" = "Twitch";
"tech_brands.hbo_max" = "HBO Max";
"tech_brands.apple_tv" = "Apple TV";
"tech_brands.steam" = "Steam";
"tech_brands.playstation" = "PlayStation";
"tech_brands.xbox" = "Xbox";
"tech_brands.nintendo" = "Nintendo";
"tech_brands.epic_games" = "Epic Games";
"tech_brands.roblox" = "Roblox";
"tech_brands.minecraft" = "Minecraft";
"tech_brands.fortnite" = "Fortnite";
"tech_brands.ebay" = "eBay";
"tech_brands.alibaba" = "Alibaba";
"tech_brands.shopify" = "Shopify";
"tech_brands.paypal" = "PayPal";
"tech_brands.stripe" = "Stripe";
"tech_brands.square" = "Square";
"tech_brands.coinbase" = "Coinbase";
"tech_brands.intel" = "Intel";
"tech_brands.amd" = "AMD";
"tech_brands.nvidia" = "NVIDIA";
"tech_brands.qualcomm" = "Qualcomm";
"tech_brands.tsmc" = "TSMC";
"tech_brands.hp" = "HP";
"tech_brands.dell" = "Dell";
"tech_brands.lenovo" = "Lenovo";
"tech_brands.adobe" = "Adobe";
"tech_brands.zoom" = "Zoom";
"tech_brands.slack" = "Slack";
"tech_brands.dropbox" = "Dropbox";
"tech_brands.notion" = "Notion";
"tech_brands.figma" = "Figma";
"tech_brands.canva" = "Canva";
"tech_brands.github" = "GitHub";
"tech_brands.whatsapp" = "WhatsApp";
"tech_brands.uber" = "Uber";
"tech_brands.airbnb" = "Airbnb";
"tech_brands.tinder" = "Tinder";
"tech_brands.duolingo" = "Duolingo";
"tech_brands.shazam" = "Shazam";
"tech_brands.waze" = "Waze";
"tech_brands.skype" = "Skype";
"tech_brands.chrome" = "Chrome";
"tech_brands.firefox" = "Firefox";
"tech_brands.safari" = "Safari";
"tech_brands.edge" = "Edge";
"tech_brands.opera" = "Opera";
"tech_brands.duckduckgo" = "DuckDuckGo";
"tech_brands.aws" = "AWS";
"tech_brands.google_cloud" = "Google Cloud";
"tech_brands.azure" = "Azure";
"tech_brands.salesforce" = "Salesforce";
"tech_brands.oracle" = "Oracle";
"tech_brands.ibm" = "IBM";
"tech_brands.vmware" = "VMware";
"netflix_shows.stranger_things" = "Stranger Things";
"netflix_shows.money_heist" = "La Casa de Papel";
"netflix_shows.the_crown" = "The Crown";
"netflix_shows.bridgerton" = "Bridgerton";
"netflix_shows.the_witcher" = "The Witcher";
"netflix_shows.ozark" = "Ozark";
"netflix_shows.house_of_cards" = "House of Cards";
"netflix_shows.orange_is_the_new_black" = "Orange Is the New Black";
"netflix_shows.black_mirror" = "Black Mirror";
"netflix_shows.the_umbrella_academy" = "The Umbrella Academy";
"netflix_shows.squid_game" = "Squid Game";
"netflix_shows.dark" = "Dark";
"netflix_shows.elite" = "Elite";
"netflix_shows.money_heist_korea" = "Money Heist Korea";
"netflix_shows.lupin" = "Lupin";
"netflix_shows.narcos" = "Narcos";
"netflix_shows.narcos_mexico" = "Narcos Mexico";
"netflix_shows.mindhunter" = "Mindhunter";
"netflix_shows.the_queens_gambit" = "The Queen's Gambit";
"netflix_shows.unorthodox" = "Unorthodox";
"netflix_shows.the_office" = "The Office";
"netflix_shows.friends" = "Friends";
"netflix_shows.brooklyn_nine_nine" = "Brooklyn Nine-Nine";
"netflix_shows.the_good_place" = "The Good Place";
"netflix_shows.schitts_creek" = "Schitt's Creek";
"netflix_shows.russian_doll" = "Russian Doll";
"netflix_shows.sex_education" = "Sex Education";
"netflix_shows.never_have_i_ever" = "Never Have I Ever";
"netflix_shows.space_force" = "Space Force";
"netflix_shows.the_kominsky_method" = "The Kominsky Method";
"netflix_shows.death_note" = "Death Note";
"netflix_shows.attack_on_titan" = "Attack on Titan";
"netflix_shows.one_piece" = "One Piece";
"netflix_shows.naruto" = "Naruto";
"netflix_shows.demon_slayer" = "Demon Slayer";
"netflix_shows.my_hero_academia" = "My Hero Academia";
"netflix_shows.jujutsu_kaisen" = "Jujutsu Kaisen";
"netflix_shows.hunter_x_hunter" = "Hunter x Hunter";
"netflix_shows.fullmetal_alchemist" = "Fullmetal Alchemist";
"netflix_shows.one_punch_man" = "One Punch Man";
"netflix_shows.the_blacklist" = "The Blacklist";
"netflix_shows.breaking_bad" = "Breaking Bad";
"netflix_shows.better_call_saul" = "Better Call Saul";
"netflix_shows.peaky_blinders" = "Peaky Blinders";
"netflix_shows.the_walking_dead" = "The Walking Dead";
"netflix_shows.lost" = "Lost";
"netflix_shows.prison_break" = "Prison Break";
"netflix_shows.suits" = "Suits";
"netflix_shows.sherlock" = "Sherlock";
"netflix_shows.lucifer" = "Lucifer";
"netflix_shows.tiger_king" = "Tiger King";
"netflix_shows.making_a_murderer" = "Making a Murderer";
"netflix_shows.wild_wild_country" = "Wild Wild Country";
"netflix_shows.the_keepers" = "The Keepers";
"netflix_shows.evil_genius" = "Evil Genius";
"netflix_shows.the_staircase" = "The Staircase";
"netflix_shows.american_murder" = "American Murder";
"netflix_shows.dont_f_with_cats" = "Don't F**k with Cats";
"netflix_shows.the_disappearance_of_madeleine_mccann" = "The Disappearance of Madeleine McCann";
"netflix_shows.our_planet" = "Our Planet";
"netflix_shows.hakan_muhafiz" = "Hakan Muhafız";
"netflix_shows.atiye" = "Atiye";
"netflix_shows.bir_baskadir" = "Bir Başkadır";
"netflix_shows.fatma_gul" = "Fatmagül";
"netflix_shows.the_gift" = "Atiye";
"netflix_shows.the_protector" = "Hakan Muhafız";
"netflix_shows.ethos" = "Bir Başkadır";
"netflix_shows.midnight" = "Midnight at the Pera Palace";
"netflix_shows.kulup" = "Kulüp";
"netflix_shows.pera_palas" = "Pera Palas'ta Gece Yarısı";
"netflix_shows.black_mirror_bandersnatch" = "Black Mirror Bandersnatch";
"netflix_shows.altered_carbon" = "Altered Carbon";
"netflix_shows.the_oa" = "The OA";
"netflix_shows.sense8" = "Sense8";
"netflix_shows.lost_in_space" = "Lost in Space";
"netflix_shows.the_rain" = "The Rain";
"netflix_shows.tribes_of_europa" = "Tribes of Europa";
"netflix_shows.the_order" = "The Order";
"netflix_shows.locke_and_key" = "Locke & Key";
"netflix_shows.the_chilling_adventures_of_sabrina" = "Chilling Adventures of Sabrina";
"netflix_shows.this_is_us" = "This Is Us";
"netflix_shows.greys_anatomy" = "Grey's Anatomy";
"netflix_shows.virgin_river" = "Virgin River";
"netflix_shows.outlander" = "Outlander";
"netflix_shows.the_good_doctor" = "The Good Doctor";
"netflix_shows.new_girl" = "New Girl";
"netflix_shows.how_to_get_away_with_murder" = "How to Get Away with Murder";
"netflix_shows.scandal" = "Scandal";
"netflix_shows.riverdale" = "Riverdale";
"netflix_shows.thirteen_reasons_why" = "13 Reasons Why";
"tech_brands" = "科技与品牌";
"tech_brands.category" = "科技与品牌";
"netflix_shows" = "Netflix 节目";
"netflix_shows.category" = "Netflix 节目";
"theme" = "主题";
"theme.dynamic" = "动态";
"theme.classic" = "经典";
"theme.neon" = "霓虹";
"theme.sunset" = "日落";
"rate_app" = "评价应用";
"rate_app_message" = "喜欢GuessUp吗？ 您在App Store上的评价对我们非常重要！";
"rate_now" = "立即评价";
"rate_later" = "稍后";
"never_rate" = "不用了，谢谢";
"rating_thank_you" = "感谢您的评价！";
"open_app_store" = "在App Store中打开";
"subscription" = "订阅";
"premium_title" = "GuessUp Premium";
"premium_subtitle" = "移除广告并解锁所有类别";
"no_ads" = "无广告体验";
"no_ads_description" = "畅享游戏，无任何广告";
"all_categories" = "所有类别";
"all_categories_description" = "无限制访问已锁定的类别";
"premium_features" = "高级功能";
"premium_features_description" = "抢先体验未来的高级功能";
"regular_updates" = "定期更新";
"regular_updates_description" = "新增类别和功能";
"free_trial_info" = "7 天免费试用";
"per_month" = "/ 月";
"cancel_anytime" = "随时取消";
"start_free_trial" = "开始免费试用";
"restore_purchases" = "恢复购买";
"terms_of_use" = "使用条款";
"privacy_policy" = "隐私政策";
"premium_active" = "高级版已激活";
"premium_benefits" = "已移除广告，所有类别已解锁";
"manage_subscription" = "管理订阅";
"upgrade_to_premium" = "升级到高级版";
"remove_ads_unlock_all" = "移除广告并解锁所有类别";
"subscription_details_title" = "订阅详情";
"subscription_name" = "GuessUp Premium 月度订阅";
"subscription_duration" = "时长：1 个月（自动续订）";
"subscription_price" = "价格：";
"subscription_auto_renewal" = "订阅会自动续订，除非在当前订阅期结束前至少 24 小时关闭自动续订";
"historical_figures.elizabeth_i" = "I. 伊丽莎白";
"historical_figures.elizabeth_ii" = "II. 伊丽莎白";
"historical_figures.victoria" = "维多利亚女王";
"historical_figures.joan_of_arc" = "圣女贞德";
"historical_figures.bell" = "亚历山大·格雷厄姆·贝尔";
"historical_figures.jobs" = "史蒂夫·乔布斯";
"historical_figures.gates" = "比尔·盖茨";
"historical_figures.musk" = "埃隆·马斯克";
"historical_figures.bezos" = "杰夫·贝索斯";
"historical_figures.mendel" = "格雷戈尔·孟德尔";
"historical_figures.turing" = "艾伦·图灵";
"historical_figures.hawking" = "斯蒂芬·霍金";
"historical_figures.pythagoras" = "毕达哥拉斯";
"historical_figures.archimedes" = "阿基米德";
"historical_figures.hypatia" = "希帕蒂娅";
"historical_figures.alhazen" = "伊本·海赛姆";
"historical_figures.avicenna" = "伊本·西那";
"historical_figures.averroes" = "伊本·鲁世德";
"historical_figures.rumi" = "梅夫拉那·杰拉勒丁·鲁米";
"historical_figures.yunus_emre" = "尤努斯·埃姆雷";
"historical_figures.haci_bektas" = "哈吉·贝克塔什·瓦里";
"historical_figures.ahmet_yesevi" = "艾哈迈德·耶塞维";
"historical_figures.ibn_battuta" = "伊本·白图泰";
"historical_figures.vespucci" = "亚美利哥·韦斯普奇";
"historical_figures.cook" = "詹姆斯·库克";
"historical_figures.armstrong" = "尼尔·阿姆斯特朗";
"historical_figures.gagarin" = "尤里·加加林";
"team_setup" = "队伍设置";
"team_setup_subtitle" = "输入队伍名称并进行比赛设置";
"team_1_name" = "1. 队伍名称";
"team_2_name" = "2. 队伍名称";
"team_1_placeholder" = "队伍A";
"team_2_placeholder" = "队伍B";
"team_1_default" = "队伍A";
"team_2_default" = "队伍B";
"number_of_rounds" = "回合数";
"party_settings" = "比赛设置";
"start_party" = "开始游戏";
"round_complete" = "回合结束";
"next_round" = "下一回合";
"finish_game" = "结束游戏";
"game_finished" = "游戏结束";
"wins" = "获胜";
"its_a_tie" = "平局";
"audio_settings" = "声音设置";
"party_music" = "派对音乐";
"sound_effects" = "音效";
"fair_play_info" = "每个队伍打的轮数都相同";
"skip" = "开始";
"next" = "下一步";
"onboarding_welcome_title" = "欢迎来到 GuessUp!";
"onboarding_welcome_description" = "一个你可以和朋友一起玩的有趣的派对游戏！ 猜单词，倾斜手机，开始玩吧。";
"onboarding_how_to_play_title" = "怎么玩？";
"onboarding_how_to_play_description" = "把手机放在你的头上，向你的朋友描述屏幕上的单词。 当他们猜对时，向上倾斜手机，当他们猜错时，向下倾斜手机。";
"onboarding_phone_movements_title" = "手机动作";
"onboarding_phone_movements_description" = "水平拿着手机，放在头上：\n正确答案向前倾斜手机 ↑\n错误答案向后倾斜手机 ↓";
"onboarding_categories_title" = "类别";
"onboarding_categories_description" = "选择不同类别的单词！ 从世界美食到名人，从动物到电影，许多类别等着你。";
"start_playing" = "开始游戏";
"tilt_forward_correct" = "向前倾\n正确";
"tilt_backward_incorrect" = "向后倾\n错误";
"how_to_play" = "玩法介绍";
"show_tutorial" = "显示教程";
"daily_objects" = "日常用品";
"daily_objects.category" = "日常用品";
"social_media_influencers" = "网红";
"social_media_influencers.category" = "网红";
"daily_objects.pan" = "煎锅";
"daily_objects.pot" = "锅";
"daily_objects.knife" = "刀";
"daily_objects.fork" = "叉子";
"daily_objects.spoon" = "勺子";
"daily_objects.plate" = "盘子";
"daily_objects.bowl" = "盘子";
"daily_objects.cup" = "杯子";
"daily_objects.glass" = "玻璃杯";
"daily_objects.cutting_board" = "砧板";
"daily_objects.can_opener" = "开罐器";
"daily_objects.whisk" = "打蛋器";
"daily_objects.spatula" = "铲子";
"daily_objects.colander" = "滤网";
"daily_objects.grater" = "刨丝器";
"daily_objects.computer_mouse" = "鼠标";
"daily_objects.keyboard" = "键盘";
"daily_objects.remote_control" = "遥控器";
"daily_objects.charger" = "充电器";
"daily_objects.headphones" = "耳机";
"daily_objects.speaker" = "扬声器";
"daily_objects.power_outlet" = "插座";
"daily_objects.extension_cord" = "延长线";
"daily_objects.flashlight" = "手电筒";
"daily_objects.calculator" = "计算器";
"daily_objects.pillow" = "枕头";
"daily_objects.blanket" = "毯子";
"daily_objects.curtain" = "窗帘";
"daily_objects.carpet" = "地毯";
"daily_objects.lamp" = "灯";
"daily_objects.mirror" = "镜子";
"daily_objects.clock" = "钟表";
"daily_objects.vase" = "花瓶";
"daily_objects.candle" = "蜡烛";
"daily_objects.picture_frame" = "相框";
"daily_objects.toothbrush" = "牙刷";
"daily_objects.toothpaste" = "牙膏";
"daily_objects.soap" = "肥皂";
"daily_objects.shampoo" = "洗发水";
"daily_objects.towel" = "毛巾";
"daily_objects.toilet_paper" = "卫生纸";
"daily_objects.hair_dryer" = "吹风机";
"daily_objects.razor" = "剃须刀";
"daily_objects.comb" = "梳子";
"daily_objects.brush" = "刷子";
"daily_objects.pen" = "铅笔";
"daily_objects.pencil" = "铅笔";
"daily_objects.eraser" = "橡皮";
"daily_objects.ruler" = "尺子";
"daily_objects.scissors" = "剪刀";
"daily_objects.stapler" = "订书机";
"daily_objects.paper_clip" = "回形针";
"daily_objects.notebook" = "笔记本";
"daily_objects.folder" = "文件";
"daily_objects.tape" = "胶带";
"social_media_influencers.cristiano_ronaldo" = "克里斯蒂亚诺·罗纳尔多";
"social_media_influencers.selena_gomez" = "赛琳娜·戈麦斯";
"social_media_influencers.mr_beast" = "MrBeast";
"social_media_influencers.lionel_messi" = "里奥·梅西";
"social_media_influencers.justin_bieber" = "贾斯汀·比伯";
"social_media_influencers.kylie_jenner" = "凯莉·詹娜";
"social_media_influencers.dwayne_johnson" = "道恩·强森";
"social_media_influencers.taylor_swift" = "泰勒·斯威夫特";
"social_media_influencers.ariana_grande" = "爱莉安娜·格兰德";
"social_media_influencers.kim_kardashian" = "金·卡戴珊";
"social_media_influencers.katy_perry" = "凯蒂·佩里";
"social_media_influencers.beyonce" = "碧昂丝";
"social_media_influencers.khloe_kardashian" = "科勒·卡戴珊";
"social_media_influencers.virat_kohli" = "维拉·科利";
"social_media_influencers.neymar_jr" = "内马尔";
"social_media_influencers.jennifer_lopez" = "詹妮弗·洛佩兹";
"social_media_influencers.kendall_jenner" = "肯达尔·詹娜";
"social_media_influencers.rihanna" = "蕾哈娜";
"social_media_influencers.nicki_minaj" = "妮琪·米娜";
"social_media_influencers.miley_cyrus" = "麦莉·赛勒斯";
"social_media_influencers.khaby_lame" = "哈比·拉姆";
"social_media_influencers.billie_eilish" = "比莉·艾利什";
"social_media_influencers.kourtney_kardashian" = "考特尼·卡戴珊";
"social_media_influencers.kevin_hart" = "凯文·哈特";
"social_media_influencers.cardi_b" = "Cardi B";
"social_media_influencers.shakira" = "夏奇拉";
"social_media_influencers.demi_lovato" = "黛米·洛瓦托";
"social_media_influencers.ellen_degeneres" = "艾伦·德杰尼勒斯";
"social_media_influencers.charli_damelio" = "查理·达梅利奥";
"social_media_influencers.lebron_james" = "勒布朗·詹姆斯";
"try_premium" = "体验尊享版";
"unlock_all_categories" = "打开所有类别，无广告";
"unlock_premium" = "开通高级功能";
"trial_toggle_subtitle" = "开始免费试用或直接订阅";
"trial_toggle_tip" = "免费试用，无风险体验！";
"subscribe_now" = "立即订阅";
"days_free_then" = "天免费，然后";
"join_premium_users" = "加入高级用户";
"social_proof_subtitle" = "数千用户选择的高级体验";
"what_users_say" = "我们的用户怎么说";
"happy_users" = "快乐用户";
"app_rating" = "应用评分";
"games_played" = "已玩游戏";
"limited_time_offer" = "限时优惠";
"join_now" = "立即加入";
"trusted_by_thousands" = "数千用户信赖";
"incredible_value" = "超值";
"value_first_subtitle" = "探索 Premium 为您带来的价值";
"compare_value" = "比较价值";
"categories_access" = "类别访问";
"basic_categories" = "基本类别";
"premium_categories" = "高级分类";
"ads_experience" = "广告体验";
"with_ads" = "带有广告";
"ad_free" = "无广告";
"game_modes" = "游戏模式";
"basic_mode" = "基础模式";
"all_modes" = "所有模式";
"updates" = "更新";
"limited_updates" = "有限更新";
"priority_updates" = "优先更新";
"cost_breakdown" = "成本分析";
"coffee_comparison" = "咖啡比较";
"less_than_coffee" = "比一杯日常咖啡更便宜";
"entertainment_value" = "娱乐价值";
"hours_of_fun" = "数小时的娱乐";
"family_time" = "家庭时光";
"priceless_moments" = "无价的瞬间";
"savings_with_trial" = "通过试用省钱";
"days_free" = "天免费";
"get_premium_value" = "获取高级价值";
"best_value_guarantee" = "最佳价值保证";
"honest_approach" = "诚实的方法";
"honest_subtitle" = "我们告诉您真相";
"honest_facts" = "诚实的真相";
"development_costs" = "开发成本";
"development_costs_desc" = "开发和维护应用程序需要资源";
"server_costs" = "服务器成本";
"server_costs_desc" = "应用程序运行的服务器和基础设施费用";
"continuous_updates" = "持续更新";
"continuous_updates_desc" = "我们不断添加新功能和类别";
"passion_project" = "激情项目";
"passion_project_desc" = "这个应用是充满爱制作的，您的支持是我们的动力";
"what_you_get" = "您将获得什么";
"honest_no_ads_desc" = "我们完全移除干扰您游戏体验的广告";
"honest_categories_desc" = "无限制访问所有类别";
"future_features" = "未来功能";
"honest_future_desc" = "优先访问新功能和类别";
"direct_support" = "直接支持";
"honest_support_desc" = "直接联系以获取您的疑问";
"transparent_pricing" = "透明定价";
"trial_period" = "试用期";
"days_completely_free" = "天完全免费";
"free" = "免费";
"after_trial" = "试用期后";
"monthly_subscription" = "月度订阅";
"cancellation" = "取消";
"cancel_anytime_honest" = "随时轻松取消";
"easy" = "容易";
"try_honestly" = "诚实试用";
"no_hidden_fees" = "无隐藏费用";
"personalized_greeting" = "为您量身定制的优惠";
"experienced_player_message" = "您是一位经验丰富的玩家！使用高级版获得更多乐趣。";
"regular_player_message" = "您经常玩！使用高级版提升您的体验。";
"new_player_message" = "您是新手！使用高级版探索所有潜力。";
"loyal_user_offer" = "作为忠实用户，您享有 20% 的特别折扣！";
"standard_offer" = "为您量身定制的高级版优惠";
"your_stats" = "您的统计数据";
"none_yet" = "暂无";
"perfect_for_you" = "给您完美的";
"advanced_categories" = "高级类别";
"advanced_categories_desc" = "适合您的体验的具有挑战性的类别";
"upgrade_now" = "立即升级";
"tailored_for_you" = "专为您定制";
"famous_brands.category" = "著名品牌";
"famous_brands.nike" = "Nike";
"famous_brands.adidas" = "Adidas";
"famous_brands.puma" = "Puma";
"famous_brands.apple" = "Apple";
"famous_brands.samsung" = "Samsung";
"famous_brands.google" = "Google";
"famous_brands.microsoft" = "Microsoft";
"famous_brands.amazon" = "Amazon";
"famous_brands.facebook" = "Facebook";
"famous_brands.tesla" = "Tesla";
"famous_brands.coca_cola" = "Coca-Cola";
"famous_brands.pepsi" = "Pepsi";
"famous_brands.mcdonalds" = "McDonald's";
"famous_brands.starbucks" = "Starbucks";
"famous_brands.ikea" = "IKEA";
"famous_brands.lego" = "Lego";
"famous_brands.toyota" = "Toyota";
"famous_brands.mercedes_benz" = "Mercedes-Benz";
"famous_brands.bmw" = "BMW";
"famous_brands.audi" = "Audi";
"famous_brands.sony" = "Sony";
"famous_brands.nintendo" = "Nintendo";
"famous_brands.netflix" = "Netflix";
"famous_brands.disney" = "Disney";
"famous_brands.louis_vuitton" = "Louis Vuitton";
"famous_brands.gucci" = "Gucci";
"famous_brands.chanel" = "Chanel";
"famous_brands.rolex" = "Rolex";
"famous_brands.zara" = "Zara";
"famous_brands.h_and_m" = "H&M";
"famous_brands.intel" = "Intel";
"famous_brands.ibm" = "IBM";
"famous_brands.oracle" = "Oracle";
"famous_brands.sap" = "SAP";
"famous_brands.ford" = "Ford";
"famous_brands.honda" = "Honda";
"famous_brands.volkswagen" = "Volkswagen";
"famous_brands.shell" = "Shell";
"famous_brands.bp" = "BP";
"famous_brands.total" = "Total";
"famous_brands.general_electric" = "General Electric";
"famous_brands.siemens" = "Siemens";
"famous_brands.nestle" = "Nestlé";
"famous_brands.unilever" = "Unilever";
"famous_brands.procter_and_gamble" = "Procter & Gamble";
"famous_brands.loreal" = "L'Oréal";
"famous_brands.colgate" = "Colgate";
"famous_brands.visa" = "Visa";
"famous_brands.mastercard" = "Mastercard";
"famous_brands.american_express" = "American Express";
"occupations" = "职业";
"famous_brands" = "著名品牌";
"occupations.category" = "职业";
"occupations.teacher" = "老师";
"occupations.doctor" = "医生";
"occupations.engineer" = "工程师";
"occupations.lawyer" = "律师";
"occupations.architect" = "建筑师";
"occupations.nurse" = "护士";
"occupations.police_officer" = "警察";
"occupations.firefighter" = "消防员";
"occupations.soldier" = "士兵";
"occupations.pilot" = "飞行员";
"occupations.astronaut" = "宇航员";
"occupations.chef" = "厨师";
"occupations.waiter" = "服务员";
"occupations.bartender" = "调酒师";
"occupations.barista" = "咖啡师";
"occupations.farmer" = "农民";
"occupations.veterinarian" = "兽医";
"occupations.pharmacist" = "药剂师";
"occupations.dentist" = "牙医";
"occupations.psychologist" = "心理学家";
"occupations.therapist" = "治疗师";
"occupations.surgeon" = "外科医生";
"occupations.judge" = "法官";
"occupations.prosecutor" = "检察官";
"occupations.journalist" = "记者";
"occupations.author" = "作家";
"occupations.poet" = "诗人";
"occupations.painter" = "画家";
"occupations.sculptor" = "雕塑家";
"occupations.musician" = "音乐家";
"occupations.singer" = "歌手";
"occupations.composer" = "作曲家";
"occupations.actor" = "演员";
"occupations.director" = "导演";
"occupations.cinematographer" = "摄影师";
"occupations.photographer" = "摄影师";
"occupations.designer" = "设计师";
"occupations.graphic_designer" = "平面设计师";
"occupations.software_developer" = "软件开发者";
"occupations.data_scientist" = "数据科学家";
"occupations.analyst" = "分析师";
"occupations.accountant" = "会计师";
"occupations.banker" = "银行家";
"occupations.economist" = "经济学家";
"occupations.marketer" = "市场营销人员";
"occupations.sales_representative" = "销售代表";
"occupations.hr_specialist" = "人力资源专家";
"occupations.manager" = "经理";
"occupations.entrepreneur" = "创业者";
"occupations.artisan" = "工匠";
"occupations.tailor" = "裁缝";
"occupations.carpenter" = "木匠";
"occupations.plumber" = "水管工";
"occupations.electrician" = "电工";
"occupations.mechanic" = "机械工";
"occupations.truck_driver" = "货车司机";
"occupations.taxi_driver" = "出租车司机";
"occupations.captain" = "船长";
"occupations.sailor" = "水手";
"occupations.fisherman" = "渔夫";
"occupations.miner" = "矿工";
"occupations.construction_worker" = "建筑工人";
"occupations.real_estate_agent" = "房地产经纪人";
"occupations.insurance_agent" = "保险经纪人";
"occupations.lecturer" = "讲师";
"occupations.researcher" = "研究员";
"occupations.librarian" = "图书管理员";
"occupations.archaeologist" = "考古学家";
"occupations.historian" = "历史学家";
"occupations.sociologist" = "社会学家";
"occupations.anthropologist" = "人类学家";
"occupations.biologist" = "生物学家";
"occupations.chemist" = "化学家";
"occupations.physicist" = "物理学家";
"occupations.mathematician" = "数学家";
"occupations.translator" = "翻译员";
"occupations.editor" = "编辑";
"occupations.animator" = "动画师";
"occupations.dancer" = "舞蹈演员";
"occupations.choreographer" = "编舞";
"occupations.athlete" = "运动员";
"occupations.coach" = "教练";
"occupations.referee" = "裁判";
"occupations.flight_attendant" = "空乘";
"occupations.tour_guide" = "导游";
"occupations.hotel_manager" = "酒店经理";
"occupations.hairdresser" = "发型师";
"occupations.beautician" = "美容师";
"occupations.masseur" = "按摩师";
"occupations.gardener" = "园丁";
"occupations.florist" = "花商";
"occupations.baker" = "面包师";
"occupations.butcher" = "屠夫";
"occupations.greengrocer" = "蔬菜水果商";
"occupations.postman" = "邮递员";
"occupations.courier" = "快递员";
"occupations.secretary" = "秘书";
"occupations.assistant" = "助手";
"occupations.security_guard" = "保安";
"occupations.cleaner" = "清洁工";
