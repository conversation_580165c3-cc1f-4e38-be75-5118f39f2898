import SwiftUI
import CoreMotion
import AudioToolbox
import GoogleMobileAds

// Reklam ID'leri için enum
private enum AdUnitID {
    static let test = "ca-app-pub-3940256099942544/4411468910"
    static let production = "ca-app-pub-2035219887278367/8728902216"
    
    static var current: String {
        #if DEBUG
        return test
        #else
        return production
        #endif
    }
}

struct GameView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("gameDuration") private var gameDuration = 30
    @AppStorage("motionSensitivity") private var motionSensitivity = 1 // 0: Düşük, 1: Or<PERSON>, 2: <PERSON><PERSON><PERSON><PERSON>
    @StateObject private var themeManager = ThemeManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var audioManager = AudioManager.shared
    let category: Category

    // Takım bilgileri
    let team1Name: String
    let team2Name: String
    let maxRounds: Int

    @State private var currentWord = ""
    @State private var timeRemaining = 30
    @State private var isGameActive = false
    @State private var showResult = false
    @State private var isCorrect = false
    @State private var motionManager = CMMotionManager()
    @State private var timer: Timer?
    @State private var currentWordIndex = 0
    @State private var countdown = 3
    @State private var showCountdown = true
    @State private var canDetectMotion = true

    // Takım skorları ve round sistemi
    @State private var team1Score = 0
    @State private var team2Score = 0
    @State private var currentRound = 1
    @State private var isTeam1Turn = true
    @State private var correctGuesses = 0
    @State private var incorrectGuesses = 0
    @State private var showScoreBoard = false
    @State private var showExitAlert = false
    @State private var isAdClosed = false
    @State private var showFinalResults = false
    
    // AdMob için state değişkenleri
    @State private var interstitial: InterstitialAd?
    @State private var showInterstitial = false
    @State private var interstitialDelegate: InterstitialAdDelegate?

    // Animasyon state değişkenleri
    @State private var showConfetti = false
    @State private var showStars = false
    @State private var confettiPosition = CGPoint.zero
    @State private var starsPosition = CGPoint.zero
    
    private var words: [String] {
        switch category.key {
        case "world_cuisine":
            return worldCuisineWords
        case "turkish_cuisine":
            return turkishCuisineWords
        case "tech_brands":
            return techBrandsWords
        case "netflix_shows":
            return netflixShowsWords
        case "turkish_celebrities":
            return turkishCelebritiesWords
        case "world_cities":
            return worldCitiesWords
        case "superheroes":
            return superheroesWords
        case "footballers":
            return footballersWords
        case "historical_figures":
            return historicalFiguresWords
        case "world_musicians":
            return worldMusiciansWords
        case "world_actors":
            return worldActorsWords
        case "bollywood_celebrities":
            return bollywoodCelebritiesWords
        case "animals":
            return animalsWords
        case "daily_objects":
            return dailyObjectsWords
        case "social_media_influencers":
            return socialMediaInfluencersWords
        case "occupations":
            return occupationsWords
        case "famous_brands":
            return famousBrandsWords
        default:
            print("Geçersiz kategori: \(category.key)")
            return ["Hata: Geçersiz kategori"]
        }
    }
    
    var body: some View {
        ZStack {
            // Dinamik arka plan gradyanı
            themeManager.backgroundGradient
                .ignoresSafeArea()
                .animation(.easeInOut(duration: 2.0), value: themeManager.currentTheme)
            
            VStack(spacing: 30) {
                // Üst bilgi çubuğu
                HStack {
                    Button(action: {
                        showExitAlert = true
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.title2.bold())
                            .foregroundColor(.white)
                            .padding()
                            .background(
                                Circle()
                                    .fill(Color.red.opacity(0.8))
                            )
                    }

                    // Takım bilgisi
                    VStack(spacing: 4) {
                        Text("Round \(currentRound)/\(maxRounds)")
                            .font(.caption.bold())
                            .foregroundColor(.white.opacity(0.8))

                        Text(isTeam1Turn ? team1Name : team2Name)
                            .font(.headline.bold())
                            .foregroundColor(.white)
                            .lineLimit(1)
                    }

                    Spacer()

                    // Süre göstergesi
                    Text("\(timeRemaining)")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .frame(width: 80, height: 80)
                        .background(
                            Circle()
                                .fill(Color.white.opacity(0.2))
                        )
                }
                .padding(.horizontal)
                .padding(.top, 20)
                
                Spacer()
                
                // Kelime gösterimi
                if !showCountdown {
                    Text(currentWord)
                        .font(.system(size: 48, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.white.opacity(0.2))
                        )
                        .padding(.horizontal)
                }
                
                Spacer()
                
                // Yönlendirme metni
                Text(NSLocalizedString("hold_phone", comment: ""))
                    .font(.system(size: 24, weight: .medium, design: .rounded))
                    .foregroundColor(.white)
                    .padding()
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupMotionManager()

            // Parti müziği başlat
            audioManager.playBackgroundMusic()

            // İlk oyun başlangıcında startCountdown() çağr
            if !isAdClosed && !showScoreBoard {
                startCountdown()
            }
            // Yatay mod için ekran yönlendirmesini ayarla
            if let windowScene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene {
                windowScene.requestGeometryUpdate(.iOS(interfaceOrientations: .landscapeLeft)) { error in
                    if error != nil {
                        print("Ekran yönlendirmesi değiştirilirken hata oluştu: \(error.localizedDescription)")
                    }
                }
            }
            // Oyun sırasında ekranın kararmamasını sağla
            UIApplication.shared.isIdleTimerDisabled = true
        }
        .onDisappear {
            stopGame()
            // Dikey moda geri dön
            if let windowScene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene {
                windowScene.requestGeometryUpdate(.iOS(interfaceOrientations: .portrait)) { error in
                    if error != nil {
                        print("Ekran yönlendirmesi değiştirilirken hata oluştu: \(error.localizedDescription)")
                    }
                }
            }
            // Oyun bittiğinde ekran kararma ayarını varsayılan duruma bırak
            // Not: Global ayar zaten true olduğu için burada değiştirmiyoruz
        }
        .alert(NSLocalizedString("exit_game", comment: ""), isPresented: $showExitAlert) {
            Button(NSLocalizedString("no", comment: ""), role: .cancel) { }
            Button(NSLocalizedString("yes", comment: ""), role: .destructive) {
                exitGame()
            }
        } message: {
            Text(NSLocalizedString("exit_game_message", comment: ""))
        }
        .onChange(of: showInterstitial) { newValue in
            if newValue {
                showInterstitialAd()
            }
        }
        .onChange(of: isAdClosed) { newValue in
            if newValue {
                showScoreBoard = true
                isAdClosed = false
            }
        }
        .overlay {
            if showResult {
                Color(isCorrect ? .green : .red)
                    .opacity(0.9)
                    .ignoresSafeArea()
                    .overlay {
                        Text(isCorrect ? NSLocalizedString("correct", comment: "") : NSLocalizedString("incorrect", comment: ""))
                            .font(.system(size: 72, weight: .bold, design: .rounded))
                            .foregroundColor(.white)
                    }
            }
            
            if showCountdown {
                Color.black.opacity(0.8)
                    .ignoresSafeArea()
                    .overlay {
                        VStack {
                            Text("\(countdown)")
                                .font(.system(size: 120, weight: .bold, design: .rounded))
                                .foregroundColor(.white)
                            
                            Text(NSLocalizedString("ready", comment: ""))
                                .font(.system(size: 36, weight: .medium, design: .rounded))
                                .foregroundColor(.white)
                                .padding(.top, 20)
                        }
                    }
            }
            
            if showScoreBoard && !showFinalResults {
                Color.black.opacity(0.8)
                    .ignoresSafeArea()
                    .overlay {
                        GeometryReader { geometry in
                            VStack(spacing: 0) {
                                // Üst kısım - Kompakt header
                                VStack(spacing: 12) {
                                    HStack {
                                        Button(action: {
                                            exitGame()
                                        }) {
                                            Image(systemName: "xmark")
                                                .font(.title3.bold())
                                                .foregroundColor(.white)
                                                .frame(width: 32, height: 32)
                                                .background(
                                                    Circle()
                                                        .fill(Color.red.opacity(0.8))
                                                )
                                        }

                                        Spacer()

                                        VStack(spacing: 2) {
                                            Text(NSLocalizedString("round_complete", comment: ""))
                                                .font(.title3.bold())
                                                .foregroundColor(.white)

                                            Text("Round \(currentRound)/\(maxRounds)")
                                                .font(.caption)
                                                .foregroundColor(.white.opacity(0.7))
                                        }

                                        Spacer()

                                        Color.clear
                                            .frame(width: 32, height: 32)
                                    }
                                }
                                .padding(.horizontal, 20)
                                .padding(.top, 15)

                                Spacer()

                                // Orta kısım - Ana içerik
                                VStack(spacing: 20) {
                                    // Aktif takım
                                    Text(isTeam1Turn ? team1Name : team2Name)
                                        .font(.title2.bold())
                                        .foregroundColor(.white)
                                        .lineLimit(1)

                                    // Bu round'un sonuçları - Kompakt ve şık
                                    HStack(spacing: 30) {
                                        VStack(spacing: 6) {
                                            Text("\(correctGuesses)")
                                                .font(.system(size: 32, weight: .bold))
                                                .foregroundColor(.green)
                                            Text(NSLocalizedString("correct", comment: ""))
                                                .font(.caption.bold())
                                                .foregroundColor(.white.opacity(0.8))
                                        }

                                        Rectangle()
                                            .fill(Color.white.opacity(0.3))
                                            .frame(width: 1, height: 50)

                                        VStack(spacing: 6) {
                                            Text("\(incorrectGuesses)")
                                                .font(.system(size: 32, weight: .bold))
                                                .foregroundColor(.red)
                                            Text(NSLocalizedString("incorrect", comment: ""))
                                                .font(.caption.bold())
                                                .foregroundColor(.white.opacity(0.8))
                                        }
                                    }
                                    .padding(.vertical, 16)
                                    .padding(.horizontal, 25)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.white.opacity(0.15))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                            )
                                    )
                                }

                                Spacer()

                                // Alt kısım - Skorlar ve buton
                                VStack(spacing: 16) {
                                    // Toplam skorlar - Çok kompakt
                                    HStack(spacing: 20) {
                                        VStack(spacing: 3) {
                                            Text(team1Name)
                                                .font(.caption.bold())
                                                .foregroundColor(.white.opacity(0.7))
                                                .lineLimit(1)
                                            Text("\(team1Score)")
                                                .font(.title3.bold())
                                                .foregroundColor(.blue)
                                        }
                                        .frame(maxWidth: .infinity)

                                        Text("VS")
                                            .font(.subheadline.bold())
                                            .foregroundColor(.white.opacity(0.5))

                                        VStack(spacing: 3) {
                                            Text(team2Name)
                                                .font(.caption.bold())
                                                .foregroundColor(.white.opacity(0.7))
                                                .lineLimit(1)
                                            Text("\(team2Score)")
                                                .font(.title3.bold())
                                                .foregroundColor(.orange)
                                        }
                                        .frame(maxWidth: .infinity)
                                    }
                                    .padding(.horizontal, 30)
                                    .padding(.vertical, 10)
                                    .background(
                                        RoundedRectangle(cornerRadius: 10)
                                            .fill(Color.white.opacity(0.1))
                                    )


                                    // Ana buton - Daha büyük ve çekici
                                    Button(action: {
                                        nextRoundOrFinish()
                                    }) {
                                        HStack(spacing: 8) {
                                            Text(currentRound < maxRounds ? NSLocalizedString("next_round", comment: "") : NSLocalizedString("finish_game", comment: ""))
                                                .font(.headline.bold())
                                                .foregroundColor(.white)

                                            Image(systemName: currentRound < maxRounds ? "arrow.right.circle.fill" : "flag.checkered.circle.fill")
                                                .font(.headline)
                                                .foregroundColor(.white)
                                        }
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 48)
                                        .background(
                                            RoundedRectangle(cornerRadius: 24)
                                                .fill(
                                                    LinearGradient(
                                                        gradient: Gradient(colors: [.green, .blue]),
                                                        startPoint: .leading,
                                                        endPoint: .trailing
                                                    )
                                                )
                                                .shadow(color: .black.opacity(0.3), radius: 6, x: 0, y: 3)
                                        )
                                    }
                                    .padding(.horizontal, 25)
                                    .padding(.bottom, 25)
                                }
                            }
                        }
                    }
            }

            // Final sonuçlar ekranı
            if showFinalResults {
                Color.black.opacity(0.9)
                    .ignoresSafeArea()
                    .overlay {
                        VStack(spacing: 30) {
                            // Kazanan takım
                            VStack(spacing: 15) {
                                Text(NSLocalizedString("game_finished", comment: ""))
                                    .font(.system(size: 36, weight: .bold, design: .rounded))
                                    .foregroundColor(.white)

                                if team1Score > team2Score {
                                    Text("\(team1Name) \(NSLocalizedString("wins", comment: ""))!")
                                        .font(.system(size: 28, weight: .bold))
                                        .foregroundColor(.yellow)
                                } else if team2Score > team1Score {
                                    Text("\(team2Name) \(NSLocalizedString("wins", comment: ""))!")
                                        .font(.system(size: 28, weight: .bold))
                                        .foregroundColor(.yellow)
                                } else {
                                    Text(NSLocalizedString("its_a_tie", comment: ""))
                                        .font(.system(size: 28, weight: .bold))
                                        .foregroundColor(.yellow)
                                }
                            }

                            // Final skorlar
                            HStack(spacing: 50) {
                                VStack(spacing: 10) {
                                    Text(team1Name)
                                        .font(.title.bold())
                                        .foregroundColor(.white)
                                    Text("\(team1Score)")
                                        .font(.system(size: 64, weight: .bold))
                                        .foregroundColor(team1Score > team2Score ? .yellow : .blue)
                                }

                                Text("-")
                                    .font(.system(size: 48, weight: .bold))
                                    .foregroundColor(.white.opacity(0.6))

                                VStack(spacing: 10) {
                                    Text(team2Name)
                                        .font(.title.bold())
                                        .foregroundColor(.white)
                                    Text("\(team2Score)")
                                        .font(.system(size: 64, weight: .bold))
                                        .foregroundColor(team2Score > team1Score ? .yellow : .orange)
                                }
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(Color.white.opacity(0.1))
                            )

                            // Butonlar
                            VStack(spacing: 15) {
                                Button(action: {
                                    restartGame()
                                }) {
                                    Text(NSLocalizedString("play_again", comment: ""))
                                        .font(.title2.bold())
                                        .foregroundColor(.white)
                                        .frame(width: 250, height: 60)
                                        .background(
                                            RoundedRectangle(cornerRadius: 30)
                                                .fill(
                                                    LinearGradient(
                                                        gradient: Gradient(colors: [.green, .blue]),
                                                        startPoint: .leading,
                                                        endPoint: .trailing
                                                    )
                                                )
                                        )
                                }

                                Button(action: {
                                    exitGame()
                                }) {
                                    Text(NSLocalizedString("back_to_menu", comment: ""))
                                        .font(.title3.bold())
                                        .foregroundColor(.white.opacity(0.8))
                                        .frame(width: 200, height: 50)
                                        .background(
                                            RoundedRectangle(cornerRadius: 25)
                                                .fill(Color.white.opacity(0.2))
                                        )
                                }
                            }
                        }
                    }
            }

            // Parçacık efektleri
            if showConfetti {
                ConfettiView(triggerPosition: confettiPosition)
            }

            if showStars {
                StarEffectView(triggerPosition: starsPosition)
            }
        }
    }
    
    private func startCountdown() {
        // Eğer reklam kapanmışsa ve skor tablosu gösteriliyorsa, geri sayımı başlatma
        if isAdClosed && showScoreBoard {
            return
        }

        countdown = 3
        showCountdown = true
        timeRemaining = gameDuration
        isGameActive = false
        currentWordIndex = 0
        currentWord = words[currentWordIndex]

        // Bu round için score değerlerini sıfırla
        correctGuesses = 0
        incorrectGuesses = 0

        // Ses efekti
        audioManager.playCountdownSound()

        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            if self.countdown > 1 {
                self.countdown -= 1
                self.audioManager.playCountdownSound()
            } else {
                self.showCountdown = false
                self.startGame()
                AnalyticsManager.shared.logGameStart(category: self.category.key)
            }
        }
    }
    
    private func vibrate() {
        // Daha güçlü titreşim için iki kez titreşim
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            generator.impactOccurred()
        }
    }

    // Hassasiyet seviyesine göre threshold değerlerini hesapla
    private func getMotionThresholds() -> (forward: Double, backward: Double, neutral: (min: Double, max: Double)) {
        switch motionSensitivity {
        case 0: // Düşük hassasiyet - daha fazla eğim gerekli
            return (forward: 0.5, backward: 2.1, neutral: (min: 0.9, max: 1.7))
        case 1: // Orta hassasiyet - varsayılan
            return (forward: 0.7, backward: 1.9, neutral: (min: 1.0, max: 1.6))
        case 2: // Yüksek hassasiyet - daha az eğim yeterli
            return (forward: 0.9, backward: 1.7, neutral: (min: 1.1, max: 1.5))
        default:
            return (forward: 0.7, backward: 1.9, neutral: (min: 1.0, max: 1.6))
        }
    }
    
    private func setupMotionManager() {
        if motionManager.isDeviceMotionAvailable {
            motionManager.deviceMotionUpdateInterval = 0.05 // Daha sık güncelleme için 0.05'e düşürdük
            motionManager.startDeviceMotionUpdates(to: .main) { motion, error in
                guard let motion = motion else { return }

                if isGameActive && canDetectMotion && !showScoreBoard {
                    let thresholds = self.getMotionThresholds()

                    // Telefon öne eğildi (BİLEMEDİ!)
                    if motion.attitude.roll < thresholds.forward && !showResult {
                        showResult = true
                        isCorrect = false
                        incorrectGuesses += 1

                        // Hata haptic feedback ve ses
                        AnimationManager.shared.playErrorHaptic()
                        vibrate()
                        audioManager.playIncorrectSound()
                        AnalyticsManager.shared.logIncorrectGuess(category: self.category.key, word: currentWord)
                    }
                    // Telefon arkaya eğildi (BİLDİ!)
                    else if motion.attitude.roll > thresholds.backward && !showResult {
                        showResult = true
                        isCorrect = true
                        correctGuesses += 1

                        // Konfeti efekti göster
                        confettiPosition = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2)
                        showConfetti = true

                        // Haptic feedback ve ses
                        AnimationManager.shared.playSuccessHaptic()
                        vibrate()
                        audioManager.playCorrectSound()
                        AnalyticsManager.shared.logCorrectGuess(category: self.category.key, word: currentWord)

                        // Konfeti efektini 2 saniye sonra kapat
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            showConfetti = false
                        }
                    }
                    // Telefon yatayda dik konuma geldi (yeni kelime için)
                    else if showResult {
                        if motion.attitude.roll >= thresholds.neutral.min && motion.attitude.roll <= thresholds.neutral.max {
                            showResult = false
                            nextWord()
                        }
                    }
                }
            }
        }
    }
    
    private func startGame() {
        if words.isEmpty || words.count == 1 {
            print("Kelime listesi boş veya geçersiz")
            return
        }
        
        // Önceki timer'ı temizle
        timer?.invalidate()
        timer = nil
        
        isGameActive = true
        timeRemaining = gameDuration
        showResult = false
        canDetectMotion = true
        showScoreBoard = false
        
        // İlk kelimeyi seç
        currentWordIndex = Int.random(in: 0..<words.count)
        currentWord = words[currentWordIndex]
        
        // Motion manager'ı yeniden başlat
        motionManager.stopDeviceMotionUpdates()
        motionManager.deviceMotionUpdateInterval = 0.05
        motionManager.startDeviceMotionUpdates(to: .main) { motion, error in
            guard let motion = motion else { return }

            if isGameActive && !showScoreBoard {
                let thresholds = self.getMotionThresholds()

                // Telefon öne eğildi (BİLEMEDİ!)
                if motion.attitude.roll < thresholds.forward && !showResult {
                    showResult = true
                    isCorrect = false
                    incorrectGuesses += 1
                    vibrate()
                    AudioServicesPlaySystemSound(1107)
                    AnalyticsManager.shared.logIncorrectGuess(category: self.category.key, word: currentWord)
                }
                // Telefon arkaya eğildi (BİLDİ!)
                else if motion.attitude.roll > thresholds.backward && !showResult {
                    showResult = true
                    isCorrect = true
                    correctGuesses += 1
                    vibrate()
                    AudioServicesPlaySystemSound(1104)
                    AnalyticsManager.shared.logCorrectGuess(category: self.category.key, word: currentWord)
                }
                // Telefon yatayda dik konuma geldi (yeni kelime için)
                else if showResult {
                    if motion.attitude.roll >= thresholds.neutral.min && motion.attitude.roll <= thresholds.neutral.max {
                        showResult = false
                        nextWord()
                    }
                }
            }
        }
        
        // Oyun timer'ını başlat
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if timeRemaining > 0 && !showScoreBoard {
                timeRemaining -= 1
            } else if !showScoreBoard {
                endGame()
            }
        }
    }
    
    private func endGame() {
        isGameActive = false
        timer?.invalidate()
        timer = nil
        motionManager.stopDeviceMotionUpdates()
        showResult = true
        canDetectMotion = false

        print("Round bitti. showInterstitial değeri: \(showInterstitial)")

        // Bu round'un skorunu ilgili takıma ekle
        if isTeam1Turn {
            team1Score += correctGuesses
        } else {
            team2Score += correctGuesses
        }

        // Skor tablosunu göster
        showScoreBoard = true

        // Round bitişi efekti
        starsPosition = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 3)
        showStars = true

        // Ses efekti
        audioManager.playGameEndSound()

        // Yıldız efektini 3 saniye sonra kapat
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            showStars = false
        }

        AnalyticsManager.shared.logRoundEnd(
            category: category.key,
            round: currentRound,
            team: isTeam1Turn ? 1 : 2,
            teamName: isTeam1Turn ? team1Name : team2Name,
            correctGuesses: correctGuesses,
            incorrectGuesses: incorrectGuesses
        )

        // Oyun tamamlandığını RatingManager'a bildir (sadece son round'da)
        if currentRound >= maxRounds {
            RatingManager.shared.gameCompleted()
        }

        // Reklam yükleme - Sadece subscription yoksa
        if !subscriptionManager.isSubscribed {
            loadInterstitial()
        }
    }
    
    private func loadInterstitial() {
        let request = Request()
        print("Reklam yükleniyor...")
        InterstitialAd.load(with: AdUnitID.current, request: request) { ad, error in
            if let error = error {
                print("Reklam yüklenirken hata oluştu: \(error.localizedDescription)")
                return
            }
            
            print("Reklam başarıyla yüklendi!")
            interstitial = ad
            interstitialDelegate = InterstitialAdDelegate(showInterstitial: $showInterstitial, isAdClosed: $isAdClosed)
            interstitial?.fullScreenContentDelegate = interstitialDelegate
            showInterstitial = true
        }
    }
    
    private func showInterstitialAd() {
        if let interstitial = interstitial {
            print("Reklam gösteriliyor...")
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let rootViewController = windowScene.windows.first?.rootViewController {
                DispatchQueue.main.async {
                    // Eğer root view controller başka bir view controller gösteriyorsa, onu kapat
                    if let presentedViewController = rootViewController.presentedViewController {
                        presentedViewController.dismiss(animated: true) {
                            interstitial.present(from: rootViewController)
                        }
                    } else {
                        interstitial.present(from: rootViewController)
                    }
                }
            } else {
                print("Root view controller bulunamadı!")
            }
        } else {
            print("Gösterilecek reklam bulunamadı!")
        }
    }
    
    private func nextWord() {
        // Rastgele yeni bir kelime seç
        var newIndex: Int
        repeat {
            newIndex = Int.random(in: 0..<words.count)
        } while newIndex == currentWordIndex && words.count > 1
        
        currentWordIndex = newIndex
        currentWord = words[currentWordIndex]
        
        // Motion manager'ı durdur
        motionManager.stopDeviceMotionUpdates()
        
        // 1.5 saniye sonra tekrar başlat (daha hızlı geçiş için)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            if isGameActive {
                motionManager.deviceMotionUpdateInterval = 0.05
                motionManager.startDeviceMotionUpdates(to: .main) { motion, error in
                    guard let motion = motion else { return }

                    if isGameActive {
                        let thresholds = self.getMotionThresholds()

                        // Telefon öne eğildi (BİLEMEDİ!)
                        if motion.attitude.roll < thresholds.forward && !showResult {
                            showResult = true
                            isCorrect = false
                            incorrectGuesses += 1
                            vibrate()
                            AudioServicesPlaySystemSound(1107)
                            AnalyticsManager.shared.logIncorrectGuess(category: self.category.key, word: currentWord)
                        }
                        // Telefon arkaya eğildi (BİLDİ!)
                        else if motion.attitude.roll > thresholds.backward && !showResult {
                            showResult = true
                            isCorrect = true
                            correctGuesses += 1
                            vibrate()
                            AudioServicesPlaySystemSound(1104)
                            AnalyticsManager.shared.logCorrectGuess(category: self.category.key, word: currentWord)
                        }
                        // Telefon yatayda dik konuma geldi (yeni kelime için)
                        else if showResult {
                            if motion.attitude.roll >= thresholds.neutral.min && motion.attitude.roll <= thresholds.neutral.max {
                                showResult = false
                                nextWord()
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func nextRoundOrFinish() {
        AnimationManager.shared.playButtonTapHaptic()

        if currentRound < maxRounds {
            // Sonraki round'a geç
            currentRound += 1
            isTeam1Turn.toggle() // Takım değiştir
            showScoreBoard = false

            // Round değişim sesi
            audioManager.playRoundChangeSound()

            // Yeni round başlat
            startCountdown()
        } else {
            // Oyunu bitir
            showScoreBoard = false
            showFinalResults = true

            // Final sesi ve konfeti
            audioManager.playApplause()
            confettiPosition = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2)
            showConfetti = true

            // Final analytics
            AnalyticsManager.shared.logGameComplete(
                category: category.key,
                team1Name: team1Name,
                team2Name: team2Name,
                team1Score: team1Score,
                team2Score: team2Score,
                rounds: maxRounds
            )

            DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                showConfetti = false
            }
        }
    }

    private func restartGame() {
        AnimationManager.shared.playButtonTapHaptic()

        // Tüm değerleri sıfırla
        team1Score = 0
        team2Score = 0
        currentRound = 1
        isTeam1Turn = true
        correctGuesses = 0
        incorrectGuesses = 0
        showFinalResults = false
        showScoreBoard = false

        // Yeni oyun başlat
        startCountdown()
    }

    private func stopGame() {
        timer?.invalidate()
        timer = nil
        motionManager.stopDeviceMotionUpdates()
        audioManager.cleanup()
    }

    private func exitGame() {
        AnalyticsManager.shared.logGameExit()
        dismiss()
    }
}

// Reklam delegate sınıfı
class InterstitialAdDelegate: NSObject, FullScreenContentDelegate {
    @Binding var showInterstitial: Bool
    @Binding var isAdClosed: Bool
    
    init(showInterstitial: Binding<Bool>, isAdClosed: Binding<Bool>) {
        _showInterstitial = showInterstitial
        _isAdClosed = isAdClosed
    }
    
    func adDidDismissFullScreenContent(_ ad: FullScreenPresentingAd) {
        print("Reklam kapatıldı")
        showInterstitial = false
        isAdClosed = true
    }
    
    func ad(_ ad: FullScreenPresentingAd, didFailToPresentFullScreenContentWithError error: Error) {
        print("Reklam gösterilirken hata oluştu: \(error.localizedDescription)")
        showInterstitial = false
        isAdClosed = true
    }
}

#Preview {
    NavigationView {
        GameView(
            category: Category(name: "Ünlüler", icon: "person.2.fill", color: .orange, key: "String"),
            team1Name: "Takım A",
            team2Name: "Takım B",
            maxRounds: 3
        )
    }
}
