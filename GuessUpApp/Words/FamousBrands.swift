import Foundation

enum FamousBrands: String, CaseIterable {
    case nike = "famous_brands.nike"
    case adidas = "famous_brands.adidas"
    case puma = "famous_brands.puma"
    case apple = "famous_brands.apple"
    case samsung = "famous_brands.samsung"
    case google = "famous_brands.google"
    case microsoft = "famous_brands.microsoft"
    case amazon = "famous_brands.amazon"
    case facebook = "famous_brands.facebook"
    case tesla = "famous_brands.tesla"
    case cocaCola = "famous_brands.coca_cola"
    case pepsi = "famous_brands.pepsi"
    case mcdonalds = "famous_brands.mcdonalds"
    case starbucks = "famous_brands.starbucks"
    case ikea = "famous_brands.ikea"
    case lego = "famous_brands.lego"
    case toyota = "famous_brands.toyota"
    case mercedesBenz = "famous_brands.mercedes_benz"
    case bmw = "famous_brands.bmw"
    case audi = "famous_brands.audi"
    case sony = "famous_brands.sony"
    case nintendo = "famous_brands.nintendo"
    case netflix = "famous_brands.netflix"
    case disney = "famous_brands.disney"
    case louisVuitton = "famous_brands.louis_vuitton"
    case gucci = "famous_brands.gucci"
    case chanel = "famous_brands.chanel"
    case rolex = "famous_brands.rolex"
    case zara = "famous_brands.zara"
    case hAndM = "famous_brands.h_and_m"
    case intel = "famous_brands.intel"
    case ibm = "famous_brands.ibm"
    case oracle = "famous_brands.oracle"
    case sap = "famous_brands.sap"
    case ford = "famous_brands.ford"
    case honda = "famous_brands.honda"
    case volkswagen = "famous_brands.volkswagen"
    case shell = "famous_brands.shell"
    case bp = "famous_brands.bp"
    case total = "famous_brands.total"
    case generalElectric = "famous_brands.general_electric"
    case siemens = "famous_brands.siemens"
    case nestle = "famous_brands.nestle"
    case unilever = "famous_brands.unilever"
    case procterAndGamble = "famous_brands.procter_and_gamble"
    case loreal = "famous_brands.loreal"
    case colgate = "famous_brands.colgate"
    case visa = "famous_brands.visa"
    case mastercard = "famous_brands.mastercard"
    case americanExpress = "famous_brands.american_express"

    var localizedName: String {
        NSLocalizedString(self.rawValue, comment: "")
    }
}

let famousBrandsWords = FamousBrands.allCases.map { $0.localizedName }
