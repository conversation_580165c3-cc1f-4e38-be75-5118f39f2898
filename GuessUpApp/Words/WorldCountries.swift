import Foundation

enum WorldCountries: String, CaseIterable {
    // Avrupa
    case turkey = "world_countries.turkey"
    case germany = "world_countries.germany"
    case france = "world_countries.france"
    case italy = "world_countries.italy"
    case spain = "world_countries.spain"
    case unitedKingdom = "world_countries.united_kingdom"
    case russia = "world_countries.russia"
    case poland = "world_countries.poland"
    case romania = "world_countries.romania"
    case netherlands = "world_countries.netherlands"
    case belgium = "world_countries.belgium"
    case greece = "world_countries.greece"
    case portugal = "world_countries.portugal"
    case czechRepublic = "world_countries.czech_republic"
    case hungary = "world_countries.hungary"
    case sweden = "world_countries.sweden"
    case austria = "world_countries.austria"
    case belarus = "world_countries.belarus"
    case switzerland = "world_countries.switzerland"
    case bulgaria = "world_countries.bulgaria"
    case serbia = "world_countries.serbia"
    case denmark = "world_countries.denmark"
    case finland = "world_countries.finland"
    case slovakia = "world_countries.slovakia"
    case norway = "world_countries.norway"
    case ireland = "world_countries.ireland"
    case croatia = "world_countries.croatia"
    case bosnia = "world_countries.bosnia"
    case albania = "world_countries.albania"
    case lithuania = "world_countries.lithuania"
    case slovenia = "world_countries.slovenia"
    case latvia = "world_countries.latvia"
    case estonia = "world_countries.estonia"
    case macedonia = "world_countries.macedonia"
    case moldova = "world_countries.moldova"
    case luxembourg = "world_countries.luxembourg"
    case malta = "world_countries.malta"
    case iceland = "world_countries.iceland"
    case montenegro = "world_countries.montenegro"
    case ukraine = "world_countries.ukraine"
    
    // Asya
    case china = "world_countries.china"
    case india = "world_countries.india"
    case indonesia = "world_countries.indonesia"
    case pakistan = "world_countries.pakistan"
    case bangladesh = "world_countries.bangladesh"
    case japan = "world_countries.japan"
    case philippines = "world_countries.philippines"
    case vietnam = "world_countries.vietnam"
    case iran = "world_countries.iran"
    case thailand = "world_countries.thailand"
    case myanmar = "world_countries.myanmar"
    case southKorea = "world_countries.south_korea"
    case iraq = "world_countries.iraq"
    case afghanistan = "world_countries.afghanistan"
    case saudiArabia = "world_countries.saudi_arabia"
    case uzbekistan = "world_countries.uzbekistan"
    case malaysia = "world_countries.malaysia"
    case nepal = "world_countries.nepal"
    case yemen = "world_countries.yemen"
    case northKorea = "world_countries.north_korea"
    case sriLanka = "world_countries.sri_lanka"
    case kazakhstan = "world_countries.kazakhstan"
    case syria = "world_countries.syria"
    case cambodia = "world_countries.cambodia"
    case jordan = "world_countries.jordan"
    case azerbaijan = "world_countries.azerbaijan"
    case uae = "world_countries.uae"
    case tajikistan = "world_countries.tajikistan"
    case israel = "world_countries.israel"
    case laos = "world_countries.laos"
    case lebanon = "world_countries.lebanon"
    case singapore = "world_countries.singapore"
    case oman = "world_countries.oman"
    case kuwait = "world_countries.kuwait"
    case georgia = "world_countries.georgia"
    case mongolia = "world_countries.mongolia"
    case armenia = "world_countries.armenia"
    case qatar = "world_countries.qatar"
    case bahrain = "world_countries.bahrain"
    case timor = "world_countries.timor"
    case cyprus = "world_countries.cyprus"
    case bhutan = "world_countries.bhutan"
    case brunei = "world_countries.brunei"
    case maldives = "world_countries.maldives"
    
    // Afrika
    case nigeria = "world_countries.nigeria"
    case ethiopia = "world_countries.ethiopia"
    case egypt = "world_countries.egypt"
    case drc = "world_countries.drc"
    case tanzania = "world_countries.tanzania"
    case southAfrica = "world_countries.south_africa"
    case kenya = "world_countries.kenya"
    case uganda = "world_countries.uganda"
    case algeria = "world_countries.algeria"
    case sudan = "world_countries.sudan"
    case morocco = "world_countries.morocco"
    case angola = "world_countries.angola"
    case ghana = "world_countries.ghana"
    case mozambique = "world_countries.mozambique"
    case madagascar = "world_countries.madagascar"
    case cameroon = "world_countries.cameroon"
    case ivoryCoast = "world_countries.ivory_coast"
    case niger = "world_countries.niger"
    case burkina = "world_countries.burkina"
    case mali = "world_countries.mali"
    case malawi = "world_countries.malawi"
    case zambia = "world_countries.zambia"
    case somalia = "world_countries.somalia"
    case senegal = "world_countries.senegal"
    case chad = "world_countries.chad"
    case zimbabwe = "world_countries.zimbabwe"
    case guinea = "world_countries.guinea"
    case rwanda = "world_countries.rwanda"
    case benin = "world_countries.benin"
    case tunisia = "world_countries.tunisia"
    case burundi = "world_countries.burundi"
    case southSudan = "world_countries.south_sudan"
    case togo = "world_countries.togo"
    case sierra = "world_countries.sierra"
    case libya = "world_countries.libya"
    case liberia = "world_countries.liberia"
    case centralAfrica = "world_countries.central_africa"
    case mauritania = "world_countries.mauritania"
    case eritrea = "world_countries.eritrea"
    case gambia = "world_countries.gambia"
    case botswana = "world_countries.botswana"
    case namibia = "world_countries.namibia"
    case gabon = "world_countries.gabon"
    case lesotho = "world_countries.lesotho"
    case guinea_bissau = "world_countries.guinea_bissau"
    case equatorial = "world_countries.equatorial"
    case mauritius = "world_countries.mauritius"
    case eswatini = "world_countries.eswatini"
    case djibouti = "world_countries.djibouti"
    case comoros = "world_countries.comoros"
    case cape_verde = "world_countries.cape_verde"
    case sao_tome = "world_countries.sao_tome"
    case seychelles = "world_countries.seychelles"
    
    // Kuzey Amerika
    case usa = "world_countries.usa"
    case mexico = "world_countries.mexico"
    case canada = "world_countries.canada"
    case guatemala = "world_countries.guatemala"
    case cuba = "world_countries.cuba"
    case haiti = "world_countries.haiti"
    case dominican = "world_countries.dominican"
    case honduras = "world_countries.honduras"
    case nicaragua = "world_countries.nicaragua"
    case costa_rica = "world_countries.costa_rica"
    case panama = "world_countries.panama"
    case jamaica = "world_countries.jamaica"
    case trinidad = "world_countries.trinidad"
    case bahamas = "world_countries.bahamas"
    case belize = "world_countries.belize"
    case barbados = "world_countries.barbados"
    case saint_lucia = "world_countries.saint_lucia"
    case grenada = "world_countries.grenada"
    case saint_vincent = "world_countries.saint_vincent"
    case antigua = "world_countries.antigua"
    case dominica = "world_countries.dominica"
    case saint_kitts = "world_countries.saint_kitts"
    
    // Güney Amerika
    case brazil = "world_countries.brazil"
    case colombia = "world_countries.colombia"
    case argentina = "world_countries.argentina"
    case peru = "world_countries.peru"
    case venezuela = "world_countries.venezuela"
    case chile = "world_countries.chile"
    case ecuador = "world_countries.ecuador"
    case bolivia = "world_countries.bolivia"
    case paraguay = "world_countries.paraguay"
    case uruguay = "world_countries.uruguay"
    case guyana = "world_countries.guyana"
    case suriname = "world_countries.suriname"
    
    // Okyanusya
    case australia = "world_countries.australia"
    case papua = "world_countries.papua"
    case new_zealand = "world_countries.new_zealand"
    case fiji = "world_countries.fiji"
    case solomon = "world_countries.solomon"
    case vanuatu = "world_countries.vanuatu"
    case samoa = "world_countries.samoa"
    case micronesia = "world_countries.micronesia"
    case tonga = "world_countries.tonga"
    case kiribati = "world_countries.kiribati"
    case palau = "world_countries.palau"
    case marshall = "world_countries.marshall"
    case tuvalu = "world_countries.tuvalu"
    case nauru = "world_countries.nauru"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let worldCountriesWords = WorldCountries.allCases.map { $0.localizedName }
