import Foundation

enum SocialMediaInfluencers: String, CaseIterable {
    // Top 30 Social Media Influencers Worldwide
    case cristianoRonaldo = "social_media_influencers.cristiano_ronaldo"
    case selenaGomez = "social_media_influencers.selena_gomez"
    case mrBeast = "social_media_influencers.mr_beast"
    case lionelMessi = "social_media_influencers.lionel_messi"
    case justinBieber = "social_media_influencers.justin_bieber"
    case kylieJenner = "social_media_influencers.kylie_jenner"
    case dwayneJ<PERSON><PERSON> = "social_media_influencers.dwayne_johnson"
    case taylorSwift = "social_media_influencers.taylor_swift"
    case arianaGrande = "social_media_influencers.ariana_grande"
    case kimKardashian = "social_media_influencers.kim_kardashian"
    case katyPerry = "social_media_influencers.katy_perry"
    case beyonce = "social_media_influencers.beyonce"
    case khloeKardashian = "social_media_influencers.khloe_kardashian"
    case viratKohli = "social_media_influencers.virat_kohli"
    case neymarJr = "social_media_influencers.neymar_jr"
    case jenniferLopez = "social_media_influencers.jennifer_lopez"
    case kendallJenner = "social_media_influencers.kendall_jenner"
    case rihanna = "social_media_influencers.rihanna"
    case nickiMinaj = "social_media_influencers.nicki_minaj"
    case mileyCyrus = "social_media_influencers.miley_cyrus"
    case khabyLame = "social_media_influencers.khaby_lame"
    case billieEilish = "social_media_influencers.billie_eilish"
    case kourtneyKardashian = "social_media_influencers.kourtney_kardashian"
    case kevinHart = "social_media_influencers.kevin_hart"
    case cardiB = "social_media_influencers.cardi_b"
    case shakira = "social_media_influencers.shakira"
    case demiLovato = "social_media_influencers.demi_lovato"
    case ellenDeGeneres = "social_media_influencers.ellen_degeneres"
    case charliDAmelio = "social_media_influencers.charli_damelio"
    case lebronJames = "social_media_influencers.lebron_james"
    
    var localizedName: String {
        return NSLocalizedString(self.rawValue, comment: "")
    }
}

let socialMediaInfluencersWords = SocialMediaInfluencers.allCases.map { $0.localizedName }
