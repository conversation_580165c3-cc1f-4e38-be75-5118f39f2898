import Foundation

enum Occupations: String, CaseIterable {
    case teacher = "occupations.teacher"
    case doctor = "occupations.doctor"
    case engineer = "occupations.engineer"
    case lawyer = "occupations.lawyer"
    case architect = "occupations.architect"
    case nurse = "occupations.nurse"
    case policeOfficer = "occupations.police_officer"
    case firefighter = "occupations.firefighter"
    case soldier = "occupations.soldier"
    case pilot = "occupations.pilot"
    case astronaut = "occupations.astronaut"
    case chef = "occupations.chef"
    case waiter = "occupations.waiter"
    case bartender = "occupations.bartender"
    case barista = "occupations.barista"
    case farmer = "occupations.farmer"
    case veterinarian = "occupations.veterinarian"
    case pharmacist = "occupations.pharmacist"
    case dentist = "occupations.dentist"
    case psychologist = "occupations.psychologist"
    case therapist = "occupations.therapist"
    case surgeon = "occupations.surgeon"
    case judge = "occupations.judge"
    case prosecutor = "occupations.prosecutor"
    case journalist = "occupations.journalist"
    case author = "occupations.author"
    case poet = "occupations.poet"
    case painter = "occupations.painter"
    case sculptor = "occupations.sculptor"
    case musician = "occupations.musician"
    case singer = "occupations.singer"
    case composer = "occupations.composer"
    case actor = "occupations.actor"
    case director = "occupations.director"
    case cinematographer = "occupations.cinematographer"
    case photographer = "occupations.photographer"
    case designer = "occupations.designer"
    case graphicDesigner = "occupations.graphic_designer"
    case softwareDeveloper = "occupations.software_developer"
    case dataScientist = "occupations.data_scientist"
    case analyst = "occupations.analyst"
    case accountant = "occupations.accountant"
    case banker = "occupations.banker"
    case economist = "occupations.economist"
    case marketer = "occupations.marketer"
    case salesRepresentative = "occupations.sales_representative"
    case hrSpecialist = "occupations.hr_specialist"
    case manager = "occupations.manager"
    case entrepreneur = "occupations.entrepreneur"
    case artisan = "occupations.artisan"
    case tailor = "occupations.tailor"
    case carpenter = "occupations.carpenter"
    case plumber = "occupations.plumber"
    case electrician = "occupations.electrician"
    case mechanic = "occupations.mechanic"
    case truckDriver = "occupations.truck_driver"
    case taxiDriver = "occupations.taxi_driver"
    case captain = "occupations.captain"
    case sailor = "occupations.sailor"
    case fisherman = "occupations.fisherman"
    case miner = "occupations.miner"
    case constructionWorker = "occupations.construction_worker"
    case realEstateAgent = "occupations.real_estate_agent"
    case insuranceAgent = "occupations.insurance_agent"
    case lecturer = "occupations.lecturer"
    case researcher = "occupations.researcher"
    case librarian = "occupations.librarian"
    case archaeologist = "occupations.archaeologist"
    case historian = "occupations.historian"
    case sociologist = "occupations.sociologist"
    case anthropologist = "occupations.anthropologist"
    case biologist = "occupations.biologist"
    case chemist = "occupations.chemist"
    case physicist = "occupations.physicist"
    case mathematician = "occupations.mathematician"
    case translator = "occupations.translator"
    case editor = "occupations.editor"
    case animator = "occupations.animator"
    case dancer = "occupations.dancer"
    case choreographer = "occupations.choreographer"
    case athlete = "occupations.athlete"
    case coach = "occupations.coach"
    case referee = "occupations.referee"
    case flightAttendant = "occupations.flight_attendant"
    case tourGuide = "occupations.tour_guide"
    case hotelManager = "occupations.hotel_manager"
    case hairdresser = "occupations.hairdresser"
    case beautician = "occupations.beautician"
    case masseur = "occupations.masseur"
    case gardener = "occupations.gardener"
    case florist = "occupations.florist"
    case baker = "occupations.baker"
    case butcher = "occupations.butcher"
    case greengrocer = "occupations.greengrocer"
    case postman = "occupations.postman"
    case courier = "occupations.courier"
    case secretary = "occupations.secretary"
    case assistant = "occupations.assistant"
    case securityGuard = "occupations.security_guard"
    case cleaner = "occupations.cleaner"

    var localizedName: String {
        NSLocalizedString(self.rawValue, comment: "")
    }
}

let occupationsWords = Occupations.allCases.map { $0.localizedName }
