import Foundation

enum DailyObjects: String, CaseIterable {
    // Mutfak Eşyaları
    case pan = "daily_objects.pan"
    case pot = "daily_objects.pot"
    case knife = "daily_objects.knife"
    case fork = "daily_objects.fork"
    case spoon = "daily_objects.spoon"
    case plate = "daily_objects.plate"
    case bowl = "daily_objects.bowl"
    case cup = "daily_objects.cup"
    case glass = "daily_objects.glass"
    case cutting_board = "daily_objects.cutting_board"
    case can_opener = "daily_objects.can_opener"
    case whisk = "daily_objects.whisk"
    case spatula = "daily_objects.spatula"
    case colander = "daily_objects.colander"
    case grater = "daily_objects.grater"
    
    // Elektronik Eşyalar
    case computer_mouse = "daily_objects.computer_mouse"
    case keyboard = "daily_objects.keyboard"
    case remote_control = "daily_objects.remote_control"
    case charger = "daily_objects.charger"
    case headphones = "daily_objects.headphones"
    case speaker = "daily_objects.speaker"
    case power_outlet = "daily_objects.power_outlet"
    case extension_cord = "daily_objects.extension_cord"
    case flashlight = "daily_objects.flashlight"
    case calculator = "daily_objects.calculator"
    
    // Ev Eşyaları
    case pillow = "daily_objects.pillow"
    case blanket = "daily_objects.blanket"
    case curtain = "daily_objects.curtain"
    case carpet = "daily_objects.carpet"
    case lamp = "daily_objects.lamp"
    case mirror = "daily_objects.mirror"
    case clock = "daily_objects.clock"
    case vase = "daily_objects.vase"
    case candle = "daily_objects.candle"
    case picture_frame = "daily_objects.picture_frame"
    
    // Banyo Eşyaları
    case toothbrush = "daily_objects.toothbrush"
    case toothpaste = "daily_objects.toothpaste"
    case soap = "daily_objects.soap"
    case shampoo = "daily_objects.shampoo"
    case towel = "daily_objects.towel"
    case toilet_paper = "daily_objects.toilet_paper"
    case hair_dryer = "daily_objects.hair_dryer"
    case razor = "daily_objects.razor"
    case comb = "daily_objects.comb"
    case brush = "daily_objects.brush"
    
    // Ofis Eşyaları
    case pen = "daily_objects.pen"
    case pencil = "daily_objects.pencil"
    case eraser = "daily_objects.eraser"
    case ruler = "daily_objects.ruler"
    case scissors = "daily_objects.scissors"
    case stapler = "daily_objects.stapler"
    case paper_clip = "daily_objects.paper_clip"
    case notebook = "daily_objects.notebook"
    case folder = "daily_objects.folder"
    case tape = "daily_objects.tape"
    
    var localizedName: String {
        NSLocalizedString(self.rawValue, comment: "")
    }
}

let dailyObjectsWords = DailyObjects.allCases.map { $0.localizedName }
